@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h1>{{ trans('plugins/training-appointment::training-appointment.name') }}</h1>
                <p>{{ trans('plugins/training-appointment::training-appointment.dashboard_description') }}</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-warning">
                                <i class="fa fa-graduation-cap text-warning"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">{{ trans('plugins/training-appointment::course.courses') }}</p>
                                <p class="card-title">{{ $stats['total_courses'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-primary">
                                <i class="fa fa-calendar text-primary"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">{{ trans('plugins/training-appointment::appointment.appointments') }}</p>
                                <p class="card-title">{{ $stats['total_appointments'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-success">
                                <i class="fa fa-cogs text-success"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">{{ trans('plugins/training-appointment::service.services') }}</p>
                                <p class="card-title">{{ $stats['total_services'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-danger">
                                <i class="fa fa-clock-o text-danger"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">{{ trans('plugins/training-appointment::appointment.pending') }}</p>
                                <p class="card-title">{{ $stats['pending_appointments'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- أحدث الدورات -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('plugins/training-appointment::course.recent_courses') }}</h4>
                </div>
                <div class="card-body">
                    @if($recentCourses->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>{{ trans('plugins/training-appointment::course.name') }}</th>
                                        <th>{{ trans('core/base::tables.created_at') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentCourses as $course)
                                        <tr>
                                            <td>
                                                <a href="{{ route('training-appointment.courses.edit', $course->id) }}">
                                                    {{ $course->title }}
                                                </a>
                                            </td>
                                            <td>{{ $course->created_at->format('Y-m-d') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p>{{ trans('plugins/training-appointment::course.no_courses') }}</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- أحدث المواعيد -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('plugins/training-appointment::appointment.recent_appointments') }}</h4>
                </div>
                <div class="card-body">
                    @if($recentAppointments->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>{{ trans('plugins/training-appointment::service.service') }}</th>
                                        <th>{{ trans('plugins/training-appointment::appointment.customer') }}</th>
                                        <th>{{ trans('core/base::tables.created_at') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentAppointments as $appointment)
                                        <tr>
                                            <td>
                                                <a href="{{ route('training-appointment.appointments.edit', $appointment->id) }}">
                                                    {{ $appointment->service->name ?? 'N/A' }}
                                                </a>
                                            </td>
                                            <td>{{ $appointment->customer->name ?? 'N/A' }}</td>
                                            <td>{{ $appointment->created_at->format('Y-m-d') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p>{{ trans('plugins/training-appointment::appointment.no_appointments') }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('plugins/training-appointment::training-appointment.quick_actions') }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ route('training-appointment.courses.create') }}" class="btn btn-primary btn-block">
                                <i class="fa fa-plus"></i> {{ trans('plugins/training-appointment::course.create') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('training-appointment.services.create') }}" class="btn btn-success btn-block">
                                <i class="fa fa-plus"></i> {{ trans('plugins/training-appointment::service.create') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('training-appointment.appointments.index') }}" class="btn btn-info btn-block">
                                <i class="fa fa-calendar"></i> {{ trans('plugins/training-appointment::appointment.manage') }}
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('training-appointment.course-categories.index') }}" class="btn btn-warning btn-block">
                                <i class="fa fa-tags"></i> {{ trans('plugins/training-appointment::course-category.manage') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
