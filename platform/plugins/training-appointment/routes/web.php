<?php

use Botble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function () {
    Route::group(['namespace' => 'Botble\TrainingAppointment\Http\Controllers', 'prefix' => 'training-appointment', 'as' => 'training-appointment.'], function () {
        // المسار الرئيسي للإضافة
        Route::get('', [
            'as' => 'index',
            'uses' => 'TrainingAppointmentController@index',
            'permission' => 'training-appointment.index',
        ]);

        // مسارات الدورات
        Route::group(['prefix' => 'courses', 'as' => 'courses.'], function () {
            Route::match(['GET', 'POST'], '', [
                'as' => 'index',
                'uses' => 'CourseController@index',
                'permission' => 'courses.index',
            ]);
            Route::get('create', [
                'as' => 'create',
                'uses' => 'CourseController@create',
                'permission' => 'courses.create',
            ]);
            Route::post('store', [
                'as' => 'store',
                'uses' => 'CourseController@store',
                'permission' => 'courses.create',
            ]);
            Route::get('{course}', [
                'as' => 'show',
                'uses' => 'CourseController@show',
                'permission' => 'courses.index',
            ]);
            Route::get('{course}/edit', [
                'as' => 'edit',
                'uses' => 'CourseController@edit',
                'permission' => 'courses.edit',
            ]);
            Route::put('{course}', [
                'as' => 'update',
                'uses' => 'CourseController@update',
                'permission' => 'courses.edit',
            ]);
            Route::delete('{course}', [
                'as' => 'destroy',
                'uses' => 'CourseController@destroy',
                'permission' => 'courses.destroy',
            ]);
            Route::delete('deletes', [
                'as' => 'deletes',
                'uses' => 'CourseController@deletes',
                'permission' => 'courses.destroy',
            ]);
        });

        // مسارات تصنيفات الدورات
        Route::group(['prefix' => 'course-categories', 'as' => 'course-categories.'], function () {
            Route::match(['GET', 'POST'], '', [
                'as' => 'index',
                'uses' => 'CourseCategoryController@index',
                'permission' => 'course-categories.index',
            ]);
            Route::get('create', [
                'as' => 'create',
                'uses' => 'CourseCategoryController@create',
                'permission' => 'course-categories.create',
            ]);
            Route::post('store', [
                'as' => 'store',
                'uses' => 'CourseCategoryController@store',
                'permission' => 'course-categories.create',
            ]);
            Route::get('{course-category}', [
                'as' => 'show',
                'uses' => 'CourseCategoryController@show',
                'permission' => 'course-categories.index',
            ]);
            Route::get('{course-category}/edit', [
                'as' => 'edit',
                'uses' => 'CourseCategoryController@edit',
                'permission' => 'course-categories.edit',
            ]);
            Route::put('{course-category}', [
                'as' => 'update',
                'uses' => 'CourseCategoryController@update',
                'permission' => 'course-categories.edit',
            ]);
            Route::delete('{course-category}', [
                'as' => 'destroy',
                'uses' => 'CourseCategoryController@destroy',
                'permission' => 'course-categories.destroy',
            ]);
            Route::delete('deletes', [
                'as' => 'deletes',
                'uses' => 'CourseCategoryController@deletes',
                'permission' => 'course-categories.destroy',
            ]);
        });

        // مسارات دروس الدورات
        Route::group(['prefix' => 'course-lessons', 'as' => 'course-lessons.'], function () {
            Route::match(['GET', 'POST'], '', [
                'as' => 'index',
                'uses' => 'CourseLessonController@index',
                'permission' => 'course-lessons.index',
            ]);
            Route::get('create', [
                'as' => 'create',
                'uses' => 'CourseLessonController@create',
                'permission' => 'course-lessons.create',
            ]);
            Route::post('store', [
                'as' => 'store',
                'uses' => 'CourseLessonController@store',
                'permission' => 'course-lessons.create',
            ]);
            Route::get('{course-lesson}', [
                'as' => 'show',
                'uses' => 'CourseLessonController@show',
                'permission' => 'course-lessons.index',
            ]);
            Route::get('{course-lesson}/edit', [
                'as' => 'edit',
                'uses' => 'CourseLessonController@edit',
                'permission' => 'course-lessons.edit',
            ]);
            Route::put('{course-lesson}', [
                'as' => 'update',
                'uses' => 'CourseLessonController@update',
                'permission' => 'course-lessons.edit',
            ]);
            Route::delete('{course-lesson}', [
                'as' => 'destroy',
                'uses' => 'CourseLessonController@destroy',
                'permission' => 'course-lessons.destroy',
            ]);
            Route::delete('deletes', [
                'as' => 'deletes',
                'uses' => 'CourseLessonController@deletes',
                'permission' => 'course-lessons.destroy',
            ]);
        });

        // مسارات الخدمات
        Route::group(['prefix' => 'services', 'as' => 'services.'], function () {
            Route::match(['GET', 'POST'], '', [
                'as' => 'index',
                'uses' => 'ServiceController@index',
                'permission' => 'services.index',
            ]);
            Route::get('create', [
                'as' => 'create',
                'uses' => 'ServiceController@create',
                'permission' => 'services.create',
            ]);
            Route::post('store', [
                'as' => 'store',
                'uses' => 'ServiceController@store',
                'permission' => 'services.create',
            ]);
            Route::get('{service}', [
                'as' => 'show',
                'uses' => 'ServiceController@show',
                'permission' => 'services.index',
            ]);
            Route::get('{service}/edit', [
                'as' => 'edit',
                'uses' => 'ServiceController@edit',
                'permission' => 'services.edit',
            ]);
            Route::put('{service}', [
                'as' => 'update',
                'uses' => 'ServiceController@update',
                'permission' => 'services.edit',
            ]);
            Route::delete('{service}', [
                'as' => 'destroy',
                'uses' => 'ServiceController@destroy',
                'permission' => 'services.destroy',
            ]);
            Route::delete('deletes', [
                'as' => 'deletes',
                'uses' => 'ServiceController@deletes',
                'permission' => 'services.destroy',
            ]);
        });

        // مسارات المواعيد
        Route::group(['prefix' => 'appointments', 'as' => 'appointments.'], function () {
            Route::match(['GET', 'POST'], '', [
                'as' => 'index',
                'uses' => 'AppointmentController@index',
                'permission' => 'appointments.index',
            ]);
            Route::get('create', [
                'as' => 'create',
                'uses' => 'AppointmentController@create',
                'permission' => 'appointments.create',
            ]);
            Route::post('store', [
                'as' => 'store',
                'uses' => 'AppointmentController@store',
                'permission' => 'appointments.create',
            ]);
            Route::get('{appointment}', [
                'as' => 'show',
                'uses' => 'AppointmentController@show',
                'permission' => 'appointments.index',
            ]);
            Route::get('{appointment}/edit', [
                'as' => 'edit',
                'uses' => 'AppointmentController@edit',
                'permission' => 'appointments.edit',
            ]);
            Route::put('{appointment}', [
                'as' => 'update',
                'uses' => 'AppointmentController@update',
                'permission' => 'appointments.edit',
            ]);
            Route::delete('{appointment}', [
                'as' => 'destroy',
                'uses' => 'AppointmentController@destroy',
                'permission' => 'appointments.destroy',
            ]);
            Route::delete('deletes', [
                'as' => 'deletes',
                'uses' => 'AppointmentController@deletes',
                'permission' => 'appointments.destroy',
            ]);
        });

        // الصفحة الرئيسية للإضافة
        Route::get('', [
            'as' => 'index',
            'uses' => 'TrainingAppointmentController@index',
            'permission' => 'training-appointment.index',
        ]);

        // مسارات البحث الموحد للإدارة
        Route::post('universal-search/reindex', [
            'uses' => 'UniversalSearchController@reindex',
            'as' => 'universal-search.reindex',
            'permission' => 'training-appointment.index',
        ]);

        // مسارات إدارة مواقع المتاجر
        Route::group(['prefix' => 'store-locations', 'as' => 'store-locations.'], function () {
                Route::get('', [
                    'as' => 'index',
                    'uses' => 'StoreLocationController@index',
                    'permission' => 'store-locations.index',
                ]);
                Route::get('create', [
                    'as' => 'create',
                    'uses' => 'StoreLocationController@create',
                    'permission' => 'store-locations.create',
                ]);
                Route::post('', [
                    'as' => 'store',
                    'uses' => 'StoreLocationController@store',
                    'permission' => 'store-locations.create',
                ]);
                Route::get('{storeLocation}', [
                    'as' => 'show',
                    'uses' => 'StoreLocationController@show',
                    'permission' => 'store-locations.index',
                ]);
                Route::get('{storeLocation}/edit', [
                    'as' => 'edit',
                    'uses' => 'StoreLocationController@edit',
                    'permission' => 'store-locations.edit',
                ]);
                Route::put('{storeLocation}', [
                    'as' => 'update',
                    'uses' => 'StoreLocationController@update',
                    'permission' => 'store-locations.edit',
                ]);
                Route::delete('{storeLocation}', [
                    'as' => 'destroy',
                    'uses' => 'StoreLocationController@destroy',
                    'permission' => 'store-locations.destroy',
                ]);
                Route::post('geocode', [
                    'as' => 'geocode',
                    'uses' => 'StoreLocationController@geocode',
                    'permission' => 'store-locations.create',
                ]);
                Route::post('reverse-geocode', [
                    'as' => 'reverse-geocode',
                    'uses' => 'StoreLocationController@reverseGeocode',
                    'permission' => 'store-locations.create',
                ]);
                Route::get('search-nearby', [
                    'as' => 'search-nearby',
                    'uses' => 'StoreLocationController@searchNearby',
                    'permission' => 'store-locations.index',
                ]);
            });
    });
});

// مسارات الواجهة الأمامية
Route::group(['namespace' => 'Botble\TrainingAppointment\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    // ملاحظة: تم نقل مسارات الواجهة الأمامية إلى ملف public.php
    // لدعم المسارات متعددة اللغات بشكل صحيح
});
