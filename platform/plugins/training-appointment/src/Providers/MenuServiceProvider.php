<?php

namespace Botble\TrainingAppointment\Providers;

use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Supports\ServiceProvider;

class MenuServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerAdminMenus();
    }

    protected function registerAdminMenus(): void
    {
        DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment',
                    'priority' => 100,
                    'name' => 'الدورات والمواعيد',
                    'icon' => 'ti ti-calendar-event',
                    'route' => 'training-appointment.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-courses',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'الدورات التدريبية',
                    'route' => 'training-appointment.courses.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-course-lessons',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'دروس الدورات',
                    'route' => 'training-appointment.course-lessons.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-course-categories',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'فئات الدورات',
                    'route' => 'training-appointment.course-categories.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-services',
                    'priority' => 4,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'الخدمات',
                    'route' => 'training-appointment.services.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-appointments',
                    'priority' => 5,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'المواعيد',
                    'route' => 'training-appointment.appointments.index',
                    'permissions' => false,
                ]);
    }
}
