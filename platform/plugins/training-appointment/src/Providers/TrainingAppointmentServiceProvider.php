<?php

namespace Bo<PERSON>ble\TrainingAppointment\Providers;

use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Supports\ServiceProvider;
use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;
use Bo<PERSON>ble\TrainingAppointment\Models\Service;
use Botble\TrainingAppointment\Models\CourseLesson;
use Botble\TrainingAppointment\Models\Appointment;
use Botble\TrainingAppointment\Listeners\UniversalSearchIndexListener;
use Botble\TrainingAppointment\Services\UniversalSearchService;
use Botble\TrainingAppointment\Commands\ReindexSearchCommand;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Slug\Facades\SlugHelper;
use Botble\Ecommerce\Models\Product;
use Illuminate\Routing\Events\RouteMatched;
use Botble\TrainingAppointment\Listeners\ProductRouteListener;
use Botble\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Illuminate\Support\Facades\Route;

class TrainingAppointmentServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(\Botble\TrainingAppointment\Repositories\Interfaces\CourseInterface::class, function () {
            return new \Botble\TrainingAppointment\Repositories\Eloquent\CourseRepository();
        });

        $this->app->bind(\Botble\TrainingAppointment\Repositories\Interfaces\AppointmentInterface::class, function () {
            return new \Botble\TrainingAppointment\Repositories\Eloquent\AppointmentRepository();
        });

        $this->app->bind(\Botble\TrainingAppointment\Repositories\Interfaces\CourseLessonInterface::class, function () {
            return new \Botble\TrainingAppointment\Repositories\Eloquent\CourseLessonRepository();
        });

        $this->app->bind(\Botble\TrainingAppointment\Repositories\Interfaces\CourseCategoryInterface::class, function () {
            return new \Botble\TrainingAppointment\Repositories\Eloquent\CourseCategoryRepository();
        });

        $this->app->singleton('training-appointment-helper', function () {
            return new \Botble\TrainingAppointment\Supports\TrainingAppointmentHelper();
        });

        $this->app->bind('TrainingAppointmentHelper', function () {
            return new \Botble\TrainingAppointment\Supports\TrainingAppointmentHelper();
        });

        // تسجيل خدمة البحث الموحد
        $this->app->singleton(UniversalSearchService::class);
    }

    public function boot(): void
    {
        // تسجيل slugs بنفس طريقة المنتجات
        SlugHelper::registerModule(Course::class, 'Courses');
        SlugHelper::registerModule(CourseCategory::class, 'Course Categories');
        SlugHelper::registerModule(Service::class, 'Services');
        SlugHelper::setPrefix(Course::class, 'courses', true);
        SlugHelper::setPrefix(CourseCategory::class, 'courses/category', true);
        SlugHelper::setPrefix(Service::class, 'services', true);

        // تحديد العمود المستخدم لتوليد الـ slug
        SlugHelper::setColumnUsedForSlugGenerator(Course::class, 'title');
        SlugHelper::setColumnUsedForSlugGenerator(CourseCategory::class, 'name');
        SlugHelper::setColumnUsedForSlugGenerator(Service::class, 'name');

        // تسجيل النماذج مع نظام اللغات المتعددة
        if (is_plugin_active('language-advanced')) {
            LanguageAdvancedManager::registerModule(Course::class, [
                'title',
                'description',
                'content',
                'location',
            ]);

            LanguageAdvancedManager::registerModule(CourseCategory::class, [
                'name',
                'description',
            ]);
        }

        $this->setNamespace('plugins/training-appointment')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes(['web', 'api']);

        // تحميل الـ routes العامة مبكراً
        $this->loadEarlyRoutes();

        // تحميل ملف public.php للـ routes الأخرى
        $this->loadRoutesFrom(__DIR__ . '/../../routes/public.php');

        // تحميل مسارات البحث
        $this->loadRoutesFrom(__DIR__ . '/../../routes/search.php');

        // تحميل مسارات البائع
        $this->loadRoutesFrom(__DIR__ . '/../../routes/vendor.php');

        // تحميل الترجمات بطريقة مباشرة
        $this->loadTranslationsFrom(__DIR__ . '/../../resources/lang', 'training-appointment');

        if (!defined('COURSE_MODULE_SCREEN_NAME')) {
            define('COURSE_MODULE_SCREEN_NAME', 'training-appointment-course');
        }

        if (!defined('APPOINTMENT_MODULE_SCREEN_NAME')) {
            define('APPOINTMENT_MODULE_SCREEN_NAME', 'training-appointment-appointment');
        }

        if (!defined('COURSE_LESSON_MODULE_SCREEN_NAME')) {
            define('COURSE_LESSON_MODULE_SCREEN_NAME', 'training-appointment-course-lesson');
        }

        if (!defined('COURSE_CATEGORY_MODULE_SCREEN_NAME')) {
            define('COURSE_CATEGORY_MODULE_SCREEN_NAME', 'training-appointment-course-category');
        }

        // إضافة عناصر القائمة الجانبية
        DashboardMenu::beforeRetrieving(function () {
            \Log::info('Training Appointment Menu Registration Called');
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment',
                    'priority' => 100,
                    'name' => 'الدورات والمواعيد',
                    'icon' => 'ti ti-calendar-event',
                    'route' => 'training-appointment.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-courses',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'الدورات التدريبية',
                    'route' => 'training-appointment.courses.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-course-lessons',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'دروس الدورات',
                    'route' => 'training-appointment.course-lessons.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-course-categories',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'فئات الدورات',
                    'route' => 'training-appointment.course-categories.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-services',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'الخدمات',
                    'route' => 'training-appointment.services.index',
                    'permissions' => false,
                ])
                ->registerItem([
                    'id' => 'cms-plugins-training-appointment-appointments',
                    'priority' => 4,
                    'parent_id' => 'cms-plugins-training-appointment',
                    'name' => 'المواعيد',
                    'route' => 'training-appointment.appointments.index',
                    'permissions' => false,
                ]);
        });

        $this->app->register(HookServiceProvider::class);
        $this->app->register(MenuServiceProvider::class);

        // إضافة عناصر قائمة البائع للدورات والمواعيد
        $this->registerVendorDashboardMenus();

        // تسجيل middleware مخصص للتأكد من الوصول للفيندور فقط
        $this->app['router']->aliasMiddleware('vendor-only', \Botble\TrainingAppointment\Http\Middleware\EnsureVendorOnly::class);

        $this->app['events']->listen(RouteMatched::class, function () {
            $this->loadViewsFrom(__DIR__ . '/../../resources/views', 'plugins/training-appointment');
        });

        // تسجيل الـ commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Botble\TrainingAppointment\Console\MigrateSlugsCommand::class,
            ]);
        }
    }

    /**
     * تسجيل عناصر قائمة البائع للدورات والمواعيد
     */
    protected function registerVendorDashboardMenus(): void
    {
        // التأكد من وجود إضافة Marketplace
        if (!is_plugin_active('marketplace')) {
            return;
        }

        // تم نقل تسجيل القائمة إلى MarketplaceServiceProvider لتجنب التضارب

        // إضافة عنصر المواعيد لقائمة العميل
        DashboardMenu::for('customer')->beforeRetrieving(function () {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-customer-appointments',
                    'priority' => 35,
                    'name' => __('training-appointment::appointment.my_appointments'),
                    'url' => fn () => route('customer.appointments'),
                    'icon' => 'ti ti-calendar-event',
                ]);
        });

        // تسجيل Event Listeners للفهرسة التلقائية
        $this->registerSearchIndexListeners();

        // تسجيل Event Listener لتوجيه البحث المدمج
        $this->app['events']->listen(RouteMatched::class, ProductRouteListener::class);

        // تسجيل أوامر Artisan
        if ($this->app->runningInConsole()) {
            $this->commands([
                ReindexSearchCommand::class,
            ]);
        }
    }

    /**
     * تسجيل Event Listeners للفهرسة التلقائية
     */
    protected function registerSearchIndexListeners(): void
    {
        $listener = app(UniversalSearchIndexListener::class);

        // Event Listeners للمنتجات
        Product::created([$listener, 'created']);
        Product::updated([$listener, 'updated']);
        Product::deleted([$listener, 'deleted']);

        // Event Listeners للخدمات
        Service::created([$listener, 'created']);
        Service::updated([$listener, 'updated']);
        Service::deleted([$listener, 'deleted']);

        // Event Listeners للدورات
        Course::created([$listener, 'created']);
        Course::updated([$listener, 'updated']);
        Course::deleted([$listener, 'deleted']);
    }

    /**
     * تحميل الـ routes المهمة مبكراً قبل الـ route العام
     */
    protected function loadEarlyRoutes(): void
    {
        Route::group(['namespace' => 'Botble\TrainingAppointment\Http\Controllers', 'as' => 'public.'], function () {
            // مسار عرض تفاصيل الخدمة - يجب أن يكون قبل الـ route العام
            Route::get('service/{slug}', [
                'uses' => 'PublicController@getServiceDetails',
                'as' => 'services.show',
            ]);

            // مسار عرض تفاصيل الدورة - يجب أن يكون قبل الـ route العام
            Route::get('course/{slug}', [
                'uses' => 'PublicController@getCourseDetails',
                'as' => 'courses.show',
            ]);
        });
    }
}
