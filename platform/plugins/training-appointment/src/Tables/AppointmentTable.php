<?php

namespace Bo<PERSON>ble\TrainingAppointment\Tables;

use <PERSON><PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Marketplace\Models\Store;
use Botble\Ecommerce\Models\Customer;
use Botble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Botble\Table\Actions\DeleteAction;
use Botble\TrainingAppointment\Models\Appointment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Botble\Table\DataTables;

class AppointmentTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Appointment::class)
            ->addActions([
                EditAction::make()
                    ->route('training-appointment.appointments.edit')
                    ->permission('appointments.edit')
                    ->label('تعديل'),
                DeleteAction::make()
                    ->route('training-appointment.appointments.destroy')
                    ->permission('appointments.destroy')
                    ->label('حذف'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('store_id', function (Appointment $item) {
                return $item->store ? $item->store->name : '—';
            })
            ->editColumn('customer_id', function (Appointment $item) {
                return $item->customer ? $item->customer->name : '—';
            })
            ->editColumn('appointment_date', function (Appointment $item) {
                return $item->appointment_date ? BaseHelper::formatDate($item->appointment_date) : '—';
            })
            ->editColumn('time', function (Appointment $item) {
                $startTime = $item->start_time ? $item->start_time->format('H:i') : '';
                $endTime = $item->end_time ? $item->end_time->format('H:i') : '';
                return $startTime && $endTime ? $startTime . ' - ' . $endTime : '—';
            })
            ->editColumn('appointment_type', function (Appointment $item) {
                return $item->appointment_type == 'in_store' ? 'في المتجر' : 'عند العميل';
            })
            ->editColumn('status', function (Appointment $item) {
                $statusClasses = [
                    'pending' => 'warning',
                    'confirmed' => 'info',
                    'completed' => 'success',
                    'cancelled' => 'danger',
                ];

                $statusLabels = [
                    'pending' => 'قيد الانتظار',
                    'confirmed' => 'مؤكد',
                    'completed' => 'مكتمل',
                    'cancelled' => 'ملغي',
                ];

                $class = $statusClasses[$item->status] ?? 'secondary';
                $label = $statusLabels[$item->status] ?? $item->status;

                return Html::tag('span', $label, ['class' => 'badge badge-' . $class]);
            })
            ->addColumn('operations', function (Appointment $item) {
                return $this->getOperations('training-appointment.appointments.edit', 'training-appointment.appointments.destroy', $item);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'store_id',
                'customer_id',
                'appointment_date',
                'start_time',
                'end_time',
                'appointment_type',
                'status',
                'created_at',
            ])
            ->with(['store', 'customer']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            'id' => [
                'title' => 'ID',
                'width' => '20px',
            ],
            'store_id' => [
                'title' => 'المتجر',
                'class' => 'text-start',
            ],
            'customer_id' => [
                'title' => 'العميل',
                'class' => 'text-start',
            ],
            'appointment_date' => [
                'title' => 'التاريخ',
                'width' => '100px',
            ],
            'time' => [
                'title' => 'الوقت',
                'width' => '100px',
                'searchable' => false,
                'orderable' => false,
            ],
            'appointment_type' => [
                'title' => 'نوع الموعد',
                'width' => '100px',
            ],
            'status' => [
                'title' => 'الحالة',
                'width' => '100px',
            ],
            'created_at' => [
                'title' => 'تاريخ الإنشاء',
                'width' => '100px',
            ],
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('training-appointment.appointments.create'), 'appointments.create');
    }

    public function bulkActions(): array
    {
        return $this->addDeleteAction(
            route('training-appointment.appointments.deletes'),
            'appointments.destroy',
            parent::bulkActions()
        );
    }

    public function getFilters(): array
    {
        return [
            'store_id' => [
                'title' => 'المتجر',
                'type' => 'select',
                'validate' => 'required|integer',
                'choices' => Store::query()->pluck('name', 'id')->toArray(),
            ],
            'customer_id' => [
                'title' => 'العميل',
                'type' => 'select',
                'validate' => 'required|integer',
                'choices' => Customer::query()->pluck('name', 'id')->toArray(),
            ],
            'appointment_date' => [
                'title' => 'تاريخ الموعد',
                'type' => 'date',
            ],
            'appointment_type' => [
                'title' => 'نوع الموعد',
                'type' => 'select',
                'choices' => [
                    'in_store' => 'في المتجر',
                    'at_customer' => 'عند العميل',
                ],
            ],
            'status' => [
                'title' => 'الحالة',
                'type' => 'select',
                'choices' => [
                    'pending' => 'قيد الانتظار',
                    'confirmed' => 'مؤكد',
                    'completed' => 'مكتمل',
                    'cancelled' => 'ملغي',
                ],
            ],
            'created_at' => [
                'title' => 'تاريخ الإنشاء',
                'type' => 'date',
            ],
        ];
    }
}
