<?php

namespace Bo<PERSON><PERSON>\TrainingAppointment\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\TrainingAppointment\Models\CourseLesson;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Bo<PERSON>ble\Table\DataTables;

class CourseLessonTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(CourseLesson::class)
            ->addActions([
                EditAction::make()->route('training-appointment.course-lessons.edit'),
                DeleteAction::make()->route('training-appointment.course-lessons.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('title', function (CourseLesson $item) {
                return BaseHelper::clean($item->title);
            })
            ->editColumn('course_id', function (CourseLesson $item) {
                return $item->course ? $item->course->title : null;
            })
            ->editColumn('checkbox', function (CourseLesson $item) {
                return view('core/table::partials.checkbox', ['id' => $item->id])->render();
            })
            ->editColumn('created_at', function (CourseLesson $item) {
                return BaseHelper::formatDate($item->created_at);
            })
            ->editColumn('status', function (CourseLesson $item) {
                return $item->status->toHtml();
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'title',
                'course_id',
                'created_at',
                'status',
            ])
            ->with(['course']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            'id' => [
                'title' => trans('core/base::tables.id'),
                'width' => '20px',
            ],
            'title' => [
                'title' => trans('core/base::tables.name'),
                'class' => 'text-start',
            ],
            'course_id' => [
                'title' => trans('plugins/training-appointment::course.form.course'),
                'class' => 'text-start',
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'width' => '100px',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'width' => '100px',
            ],
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('training-appointment.course-lessons.create'), 'training-appointment.course-lessons.create');
    }

    public function bulkActions(): array
    {
        return parent::bulkActions() + [
            DeleteBulkAction::make()
                ->action('DELETE')
                ->permission('training-appointment.course-lessons.destroy')
                ->dispatchUrl(route('training-appointment.course-lessons.deletes')),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'title' => [
                'title' => trans('core/base::tables.name'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }
}
