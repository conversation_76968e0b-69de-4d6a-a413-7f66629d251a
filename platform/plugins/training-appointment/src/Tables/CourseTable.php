<?php

namespace Bo<PERSON><PERSON>\TrainingAppointment\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Marketplace\Models\Store;
use Botble\Media\Facades\RvMedia;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\EditAction;
use Botble\Table\Actions\DeleteAction;
use Botble\TrainingAppointment\Models\Course;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Botble\Table\DataTables;

class CourseTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Course::class)
            ->addActions([
                EditAction::make()->route('training-appointment.courses.edit'),
                DeleteAction::make()->route('training-appointment.courses.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('title', function (Course $item) {
                if (Auth::check() && Auth::user()->hasPermission('courses.edit')) {
                    return Html::link(route('training-appointment.courses.edit', $item->id), $item->title);
                }
                return $item->title;
            })
            ->editColumn('image', function (Course $item) {
                return Html::image(
                    RvMedia::getImageUrl($item->image, 'thumb', false, RvMedia::getDefaultImage()),
                    trans('core/base::tables.image'),
                    ['width' => 50]
                );
            })
            ->editColumn('store_id', function (Course $item) {
                return $item->store ? $item->store->name : '—';
            })
            ->editColumn('price', function (Course $item) {
                return $item->formatted_price;
            })
            ->editColumn('start_date', function (Course $item) {
                return $item->start_date ? BaseHelper::formatDate($item->start_date) : '—';
            })
            ->editColumn('status', function (Course $item) {
                return $item->status->toHtml();
            })
            ->addColumn('operations', function (Course $item) {
                $editButton = '';
                $deleteButton = '';

                if (Auth::check() && Auth::user()->hasPermission('courses.edit')) {
                    $editButton = Html::link(
                        route('training-appointment.courses.edit', $item->id),
                        '<i class="ti ti-edit"></i>',
                        [
                            'class' => 'btn btn-icon btn-sm btn-primary',
                            'title' => trans('core/base::tables.edit'),
                            'data-bs-toggle' => 'tooltip',
                        ],
                        null,
                        false
                    );
                }

                if (Auth::check() && Auth::user()->hasPermission('courses.destroy')) {
                    $deleteButton = Html::link(
                        '#',
                        '<i class="ti ti-trash"></i>',
                        [
                            'class' => 'btn btn-icon btn-sm btn-danger deleteDialog',
                            'title' => trans('core/base::tables.delete_entry'),
                            'data-section' => route('training-appointment.courses.destroy', $item->id),
                            'data-bs-toggle' => 'tooltip',
                        ],
                        null,
                        false
                    );
                }

                return $editButton . ' ' . $deleteButton;
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'title',
                'image',
                'store_id',
                'price',
                'start_date',
                'status',
                'created_at',
            ])
            ->with(['store']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            'id' => [
                'title' => 'ID',
                'width' => '20px',
            ],
            'image' => [
                'title' => 'الصورة',
                'width' => '70px',
            ],
            'title' => [
                'title' => 'العنوان',
                'class' => 'text-start',
            ],
            'store_id' => [
                'title' => 'المتجر',
                'class' => 'text-start',
            ],
            'price' => [
                'title' => 'السعر',
            ],
            'start_date' => [
                'title' => 'تاريخ البدء',
                'width' => '100px',
            ],
            'status' => [
                'title' => 'الحالة',
                'width' => '100px',
            ],
            'created_at' => [
                'title' => 'تاريخ الإنشاء',
                'width' => '100px',
            ],
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('training-appointment.courses.create'), 'courses.create');
    }

    public function bulkActions(): array
    {
        return $this->addDeleteAction(
            route('training-appointment.courses.deletes'),
            'courses.destroy',
            parent::bulkActions()
        );
    }

    public function getFilters(): array
    {
        return [
            'title' => [
                'title' => 'العنوان',
                'type' => 'text',
                'validate' => 'required|string|max:120',
            ],
            'store_id' => [
                'title' => 'المتجر',
                'type' => 'select',
                'validate' => 'required|integer',
                'choices' => Store::query()->pluck('name', 'id')->toArray(),
            ],
            'status' => [
                'title' => 'الحالة',
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => 'تاريخ الإنشاء',
                'type' => 'date',
            ],
        ];
    }
}
