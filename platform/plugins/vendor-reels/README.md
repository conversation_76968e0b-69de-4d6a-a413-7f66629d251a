# Vendor Reels & Messaging Plugin

نظام شامل للريلز والمحادثات - يتيح للبائعين إنشاء مقاطع فيديو قصيرة والتواصل مع العملاء مثل الماسنجر.

## 🎯 الميزات الرئيسية

### 🎬 نظام الريلز للبائعين
- إنشاء وإدارة مقاطع فيديو قصيرة (ريلز)
- رفع الفيديوهات والصور المصغرة
- ربط المنتجات بالفيديوهات مع تحديد المواقع والأوقات
- إحصائيات مفصلة (مشاهدات، إعجابات، تعليقات، مشاركات)
- إدارة حالة النشر والميزات المميزة
- تحليلات الأداء والتفاعل

### 💬 نظام المحادثات (مثل الماسنجر)
- محادثات خاصة بين العملاء والبائعين
- محادثات جماعية مع إدارة المشاركين
- إرسال رسائل نصية، صور، فيديوهات، ملفات
- مشاركة الريلز والمنتجات في المحادثات
- مشاركة المواقع الجغرافية
- إيصالات القراءة ومؤشر الكتابة
- إشعارات فورية (Real-time)
- تعديل وحذف الرسائل
- الرد على الرسائل
- حفظ الرسائل المفضلة

### 👥 للعملاء
- تصفح الريلز بواجهة جذابة
- التفاعل مع الريلز (إعجاب، تعليق، مشاركة، حفظ)
- عرض المنتجات المرتبطة بالفيديو
- البحث والفلترة
- مشاهدة ريلز متجر محدد
- بدء محادثات مع البائعين
- إدارة المحادثات والرسائل

### 🔧 للمطورين
- API شامل للتطبيقات المحمولة
- نظام Events و Listeners
- Commands للصيانة والتنظيف
- Widgets للإحصائيات
- نظام Permissions متقدم
- Broadcasting للتحديثات الفورية
- نظام إشعارات متقدم

## التثبيت

### 1. تفعيل الـ Plugin
```bash
php artisan cms:plugin:activate vendor-reels
```

### 2. تشغيل الـ Migrations
```bash
php artisan migrate
```

### 3. نشر الـ Assets (اختياري)
```bash
php artisan vendor:publish --tag=vendor-reels-assets
```

### 4. إنشاء بيانات تجريبية (اختياري)
```bash
php artisan db:seed --class="Botble\VendorReels\Database\Seeders\VendorReelsSeeder"
```

## الاستخدام

### للبائعين

#### إنشاء ريل جديد
1. انتقل إلى لوحة تحكم البائع
2. اختر "الريلز" من القائمة الجانبية
3. انقر على "إضافة ريل جديد"
4. املأ المعلومات المطلوبة:
   - العنوان والوصف
   - رفع الفيديو (حد أقصى 100MB، 5 دقائق)
   - رفع الصورة المصغرة (اختياري)
   - ربط المنتجات مع تحديد المواقع والأوقات
5. اختر حالة النشر (مسودة أو منشور)
6. احفظ الريل

#### إدارة الريلز
- عرض قائمة جميع الريلز مع الإحصائيات
- تعديل الريلز الموجودة
- تغيير حالة النشر
- عرض التحليلات المفصلة
- حذف الريلز غير المرغوب فيها

### للعملاء

#### تصفح الريلز
- زيارة صفحة `/reels` لعرض جميع الريلز
- استخدام البحث والفلاتر
- عرض ريلز متجر محدد
- مشاهدة الريلز المميزة والشائعة

#### التفاعل
- الإعجاب بالريلز
- إضافة تعليقات
- مشاركة الريلز
- حفظ الريلز المفضلة
- عرض المنتجات المرتبطة والشراء المباشر

## API Documentation

### المسارات العامة

#### جلب قائمة الريلز
```http
GET /api/v1/reels
```

**Parameters:**
- `search` (string): البحث في العنوان والوصف
- `store_id` (integer): فلترة حسب المتجر
- `per_page` (integer): عدد العناصر في الصفحة (حد أقصى 50)

#### جلب ريل محدد
```http
GET /api/v1/reels/{id}
```

#### تسجيل مشاهدة
```http
POST /api/v1/reels/{id}/view
```

#### الإعجاب بريل
```http
POST /api/v1/reels/{id}/like
Authorization: Bearer {token}
```

#### إلغاء الإعجاب
```http
DELETE /api/v1/reels/{id}/like
Authorization: Bearer {token}
```

#### إضافة تعليق
```http
POST /api/v1/reels/{id}/comment
Authorization: Bearer {token}
Content-Type: application/json

{
    "content": "نص التعليق"
}
```

#### حفظ ريل
```http
POST /api/v1/reels/{id}/save
Authorization: Bearer {token}
```

#### إلغاء حفظ ريل
```http
DELETE /api/v1/reels/{id}/save
Authorization: Bearer {token}
```

#### مشاركة ريل
```http
POST /api/v1/reels/{id}/share
Authorization: Bearer {token}
Content-Type: application/json

{
    "platform": "whatsapp"
}
```

### مسارات المحادثات

#### جلب قائمة المحادثات
```http
GET /api/v1/conversations
Authorization: Bearer {token}
```

#### إنشاء محادثة جديدة
```http
POST /api/v1/conversations
Authorization: Bearer {token}
Content-Type: application/json

{
    "type": "private",
    "participant_ids": [2, 3],
    "title": "عنوان المحادثة",
    "initial_message": "مرحباً"
}
```

#### بدء محادثة مع متجر
```http
POST /api/v1/conversations/start-with-store
Authorization: Bearer {token}
Content-Type: application/json

{
    "store_id": 1,
    "initial_message": "مرحباً، أريد الاستفسار عن منتجاتكم"
}
```

#### جلب رسائل المحادثة
```http
GET /api/v1/conversations/{id}/messages
Authorization: Bearer {token}
```

#### إرسال رسالة نصية
```http
POST /api/v1/conversations/{id}/messages/text
Authorization: Bearer {token}
Content-Type: application/json

{
    "content": "نص الرسالة",
    "reply_to_id": 123
}
```

#### إرسال ملف وسائط
```http
POST /api/v1/conversations/{id}/messages/media
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [ملف الصورة/الفيديو/الصوت]
type: "image"
caption: "وصف الملف"
reply_to_id: 123
```

#### إرسال ريل في المحادثة
```http
POST /api/v1/conversations/{id}/messages/reel
Authorization: Bearer {token}
Content-Type: application/json

{
    "reel_id": 1,
    "caption": "شاهد هذا الريل الرائع"
}
```

#### إرسال منتج في المحادثة
```http
POST /api/v1/conversations/{id}/messages/product
Authorization: Bearer {token}
Content-Type: application/json

{
    "product_id": 1,
    "caption": "ما رأيك في هذا المنتج؟"
}
```

#### إرسال موقع جغرافي
```http
POST /api/v1/conversations/{id}/messages/location
Authorization: Bearer {token}
Content-Type: application/json

{
    "latitude": 24.7136,
    "longitude": 46.6753,
    "address": "الرياض، المملكة العربية السعودية"
}
```

#### تعديل رسالة
```http
PUT /api/v1/messages/{id}/edit
Authorization: Bearer {token}
Content-Type: application/json

{
    "content": "النص المعدل"
}
```

#### حذف رسالة
```http
DELETE /api/v1/messages/{id}
Authorization: Bearer {token}
```

#### تحديد رسالة كمقروءة
```http
POST /api/v1/messages/{id}/mark-as-read
Authorization: Bearer {token}
```

### مسارات البائعين

#### جلب ريلز البائع
```http
GET /api/v1/vendor/reels
Authorization: Bearer {token}
```

#### إنشاء ريل جديد
```http
POST /api/v1/vendor/reels
Authorization: Bearer {token}
Content-Type: application/json

{
    "title": "عنوان الريل",
    "description": "وصف الريل",
    "video_url": "رابط الفيديو",
    "thumbnail_url": "رابط الصورة المصغرة",
    "duration": 60,
    "status": "published",
    "is_featured": false,
    "products": [
        {
            "product_id": 1,
            "position_x": 50,
            "position_y": 30,
            "timestamp_start": 10,
            "timestamp_end": 40
        }
    ]
}
```

## التكوين

### إعدادات الفيديو
```php
// config/plugins/vendor-reels/general.php
'video' => [
    'max_size' => 104857600, // 100MB
    'max_duration' => 300, // 5 دقائق
    'allowed_formats' => ['mp4', 'mov', 'avi'],
],
```

### إعدادات الأمان
```php
'security' => [
    'max_reels_per_day' => 10,
    'auto_approve_reels' => false,
    'enable_content_moderation' => true,
],
```

## Commands

### تنظيف الريلز القديمة
```bash
# عرض الريلز التي سيتم حذفها (تشغيل تجريبي)
php artisan vendor-reels:cleanup --days=90 --dry-run

# حذف الريلز الأقدم من 90 يوم
php artisan vendor-reels:cleanup --days=90

# حذف فوري بدون تأكيد
php artisan vendor-reels:cleanup --days=90 --force
```

## Events

### ReelCreated
يتم إطلاقه عند إنشاء ريل جديد
```php
Event::listen(CreatedContentEvent::class, function ($event) {
    if ($event->screen === 'vendor-reel') {
        // معالجة إنشاء ريل جديد
    }
});
```

### ReelUpdated
يتم إطلاقه عند تحديث ريل
```php
Event::listen(UpdatedContentEvent::class, function ($event) {
    if ($event->screen === 'vendor-reel') {
        // معالجة تحديث الريل
    }
});
```

## Helper Functions

```php
// تنسيق مدة الفيديو
format_video_duration(90); // "1:30"

// تنسيق عدد المشاهدات
format_views_count(1500); // "1.5K"

// حساب معدل التفاعل
get_reel_engagement_rate(1000, 50, 10, 5); // 6.5

// التحقق من إمكانية إنشاء ريل
can_user_create_reel($userId); // true/false

// الحصول على إعدادات الريلز
vendor_reels_config('video.max_size'); // 104857600
```

## المتطلبات

- PHP 8.1+
- Laravel 10+
- Botble CMS 7.3+
- Marketplace Plugin
- Ecommerce Plugin
- FFmpeg (اختياري لمعالجة الفيديو)

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا الـ Plugin مرخص تحت رخصة MIT.
