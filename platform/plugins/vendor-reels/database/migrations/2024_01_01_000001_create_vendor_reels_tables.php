<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vendor_reels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_id')->constrained('mp_stores')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('video_url');
            $table->string('thumbnail_url')->nullable();
            $table->integer('duration')->default(0); // بالثواني
            $table->bigInteger('views_count')->default(0);
            $table->bigInteger('likes_count')->default(0);
            $table->bigInteger('comments_count')->default(0);
            $table->bigInteger('shares_count')->default(0);
            $table->string('status', 60)->default('published');
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            $table->index(['store_id', 'status']);
            $table->index(['status', 'published_at']);
            $table->index(['is_featured', 'status']);
        });

        Schema::create('reel_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reel_id')->constrained('vendor_reels')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->decimal('position_x', 5, 2)->nullable(); // موقع التاغ في الفيديو (X)
            $table->decimal('position_y', 5, 2)->nullable(); // موقع التاغ في الفيديو (Y)
            $table->decimal('timestamp_start', 8, 2)->nullable(); // وقت ظهور التاغ
            $table->decimal('timestamp_end', 8, 2)->nullable(); // وقت اختفاء التاغ
            $table->timestamps();

            $table->unique(['reel_id', 'product_id']);
        });

        Schema::create('reel_interactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reel_id')->constrained('vendor_reels')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('ec_customers')->onDelete('cascade');
            $table->enum('type', ['like', 'comment', 'share', 'save', 'view']);
            $table->text('content')->nullable(); // للتعليقات
            $table->string('ip_address', 45)->nullable();
            $table->timestamps();

            $table->index(['reel_id', 'type']);
            $table->index(['customer_id', 'type']);
            // إزالة القيد الفريد للسماح بتعليقات ومشاركات متعددة
            // فقط like, save, view يجب أن تكون فريدة لكل مستخدم
            $table->unique(['reel_id', 'customer_id', 'type'], 'unique_reel_customer_single_interaction')
                  ->where('type', 'in', ['like', 'save']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reel_interactions');
        Schema::dropIfExists('reel_products');
        Schema::dropIfExists('vendor_reels');
    }
};
