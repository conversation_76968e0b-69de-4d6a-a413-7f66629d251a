<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // إزالة القيد الفريد الحالي
        Schema::table('reel_interactions', function (Blueprint $table) {
            $table->dropUnique('unique_reel_customer_interaction');
        });

        // إضافة قيود فريدة منفصلة للتفاعلات المختلفة
        Schema::table('reel_interactions', function (Blueprint $table) {
            // قيد فريد للإعجابات (مستخدم واحد يعجب مرة واحدة فقط)
            $table->unique(['reel_id', 'customer_id'], 'unique_reel_customer_like')
                  ->where('type', 'like');
            
            // قيد فريد للحفظ (مستخدم واحد يحفظ مرة واحدة فقط)
            $table->unique(['reel_id', 'customer_id'], 'unique_reel_customer_save')
                  ->where('type', 'save');
        });
    }

    public function down(): void
    {
        Schema::table('reel_interactions', function (Blueprint $table) {
            // إزالة القيود الجديدة
            $table->dropUnique('unique_reel_customer_like');
            $table->dropUnique('unique_reel_customer_save');
            
            // إعادة القيد القديم
            $table->unique(['reel_id', 'customer_id', 'type'], 'unique_reel_customer_interaction');
        });
    }
};
