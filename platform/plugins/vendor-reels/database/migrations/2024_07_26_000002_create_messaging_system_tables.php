<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول المحادثات
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['private', 'group', 'support', 'store_customer'])->default('private');
            $table->string('title')->nullable(); // للمحادثات الجماعية
            $table->foreignId('last_message_id')->nullable()->constrained('messages')->onDelete('set null');
            $table->timestamp('last_message_at')->nullable();
            $table->boolean('is_group')->default(false);
            $table->foreignId('created_by')->constrained('ec_customers')->onDelete('cascade');
            $table->foreignId('store_id')->nullable()->constrained('mp_stores')->onDelete('cascade');
            $table->timestamps();

            $table->index(['type', 'last_message_at']);
            $table->index(['store_id', 'type']);
        });

        // جدول الرسائل
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained('conversations')->onDelete('cascade');
            $table->foreignId('sender_id')->constrained('ec_customers')->onDelete('cascade');
            $table->text('content')->nullable();
            $table->enum('type', ['text', 'image', 'video', 'audio', 'file', 'reel', 'product', 'location', 'system'])->default('text');
            $table->string('attachment_url')->nullable();
            $table->string('attachment_type')->nullable(); // MIME type
            $table->bigInteger('attachment_size')->nullable(); // بالبايت
            $table->foreignId('reply_to_id')->nullable()->constrained('messages')->onDelete('set null');
            $table->boolean('is_edited')->default(false);
            $table->timestamp('edited_at')->nullable();
            $table->string('messageable_type')->nullable(); // للربط مع الريلز أو المنتجات
            $table->bigInteger('messageable_id')->nullable();
            $table->timestamps();

            $table->index(['conversation_id', 'created_at']);
            $table->index(['sender_id', 'created_at']);
            $table->index(['messageable_type', 'messageable_id']);
        });

        // جدول المشاركين في المحادثات
        Schema::create('conversation_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained('conversations')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->timestamp('joined_at')->default(now());
            $table->timestamp('left_at')->nullable();
            $table->boolean('is_admin')->default(false);
            $table->boolean('is_muted')->default(false);
            $table->timestamps();

            $table->unique(['conversation_id', 'customer_id']);
            $table->index(['customer_id', 'left_at']);
        });

        // جدول إيصالات القراءة
        Schema::create('message_read_receipts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('message_id')->constrained('messages')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->timestamp('read_at')->default(now());
            $table->timestamps();

            $table->unique(['message_id', 'customer_id']);
            $table->index(['customer_id', 'read_at']);
        });

        // إضافة foreign key للرسالة الأخيرة بعد إنشاء جدول الرسائل
        Schema::table('conversations', function (Blueprint $table) {
            $table->foreign('last_message_id')->references('id')->on('messages')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('conversations', function (Blueprint $table) {
            $table->dropForeign(['last_message_id']);
        });
        
        Schema::dropIfExists('message_read_receipts');
        Schema::dropIfExists('conversation_participants');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('conversations');
    }
};
