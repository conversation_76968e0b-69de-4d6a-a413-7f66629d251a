<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول المتابعة والأصدقاء
        Schema::create('user_follows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('follower_id')->constrained('ec_customers')->onDelete('cascade');
            $table->foreignId('following_id')->constrained('ec_customers')->onDelete('cascade');
            $table->enum('status', ['pending', 'accepted', 'blocked', 'unfollowed'])->default('accepted');
            $table->timestamp('followed_at')->nullable();
            $table->timestamp('unfollowed_at')->nullable();
            $table->timestamps();

            $table->unique(['follower_id', 'following_id']);
            $table->index(['follower_id', 'status']);
            $table->index(['following_id', 'status']);
        });

        // جدول القصص/الحالات
        Schema::create('user_stories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->foreignId('store_id')->nullable()->constrained('mp_stores')->onDelete('cascade');
            $table->text('content')->nullable(); // للنصوص
            $table->string('media_url')->nullable(); // للصور والفيديوهات
            $table->enum('media_type', ['image', 'video', 'text'])->default('text');
            $table->string('background_color', 7)->default('#000000'); // للقصص النصية
            $table->string('text_color', 7)->default('#FFFFFF');
            $table->integer('duration')->default(15); // مدة العرض بالثواني
            $table->bigInteger('views_count')->default(0);
            $table->timestamp('expires_at'); // انتهاء صلاحية القصة (24 ساعة عادة)
            $table->boolean('is_active')->default(true);
            $table->enum('privacy', ['public', 'followers', 'close_friends', 'private'])->default('followers');
            $table->timestamps();

            $table->index(['user_id', 'is_active', 'expires_at']);
            $table->index(['store_id', 'is_active', 'expires_at']);
            $table->index(['privacy', 'is_active', 'expires_at']);
        });

        // جدول مشاهدات القصص
        Schema::create('story_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('story_id')->constrained('user_stories')->onDelete('cascade');
            $table->foreignId('viewer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('ip_address', 45)->nullable();
            $table->timestamp('viewed_at')->default(now());
            $table->timestamps();

            $table->unique(['story_id', 'viewer_id']);
            $table->index(['viewer_id', 'viewed_at']);
        });

        // جدول الإشعارات المتقدمة
        Schema::create('user_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('type', 50); // نوع الإشعار
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // بيانات إضافية
            $table->string('notifiable_type')->nullable(); // نوع الكائن المرتبط
            $table->bigInteger('notifiable_id')->nullable(); // معرف الكائن المرتبط
            $table->string('action_url')->nullable(); // رابط الإجراء
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->timestamps();

            $table->index(['user_id', 'is_read', 'created_at']);
            $table->index(['type', 'created_at']);
            $table->index(['notifiable_type', 'notifiable_id']);
            $table->index(['priority', 'is_read']);
        });

        // جدول الأصدقاء المقربين
        Schema::create('close_friends', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->foreignId('friend_id')->constrained('ec_customers')->onDelete('cascade');
            $table->timestamp('added_at')->default(now());
            $table->timestamps();

            $table->unique(['user_id', 'friend_id']);
            $table->index(['user_id', 'added_at']);
        });

        // جدول حظر المستخدمين
        Schema::create('user_blocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('blocker_id')->constrained('ec_customers')->onDelete('cascade');
            $table->foreignId('blocked_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('reason')->nullable();
            $table->timestamp('blocked_at')->default(now());
            $table->timestamps();

            $table->unique(['blocker_id', 'blocked_id']);
            $table->index(['blocker_id', 'blocked_at']);
            $table->index(['blocked_id', 'blocked_at']);
        });

        // جدول الإعجابات العامة (للريلز والقصص والتعليقات)
        Schema::create('user_likes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('likeable_type'); // نوع الكائن (reel, story, comment)
            $table->bigInteger('likeable_id'); // معرف الكائن
            $table->timestamp('liked_at')->default(now());
            $table->timestamps();

            $table->unique(['user_id', 'likeable_type', 'likeable_id']);
            $table->index(['likeable_type', 'likeable_id']);
            $table->index(['user_id', 'liked_at']);
        });

        // جدول المشاركات العامة
        Schema::create('user_shares', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('shareable_type'); // نوع الكائن
            $table->bigInteger('shareable_id'); // معرف الكائن
            $table->string('platform')->nullable(); // منصة المشاركة
            $table->string('share_url')->nullable(); // رابط المشاركة
            $table->timestamp('shared_at')->default(now());
            $table->timestamps();

            $table->index(['shareable_type', 'shareable_id']);
            $table->index(['user_id', 'shared_at']);
            $table->index(['platform', 'shared_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_shares');
        Schema::dropIfExists('user_likes');
        Schema::dropIfExists('user_blocks');
        Schema::dropIfExists('close_friends');
        Schema::dropIfExists('user_notifications');
        Schema::dropIfExists('story_views');
        Schema::dropIfExists('user_stories');
        Schema::dropIfExists('user_follows');
    }
};
