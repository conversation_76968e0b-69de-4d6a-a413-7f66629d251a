class MessengerApp {
    constructor() {
        this.currentConversationId = null;
        this.conversations = [];
        this.messages = [];
        this.apiToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        this.init();
    }

    init() {
        this.loadConversations();
        this.bindEvents();
        this.setupWebSocket();
    }

    bindEvents() {
        // إرسال رسالة
        document.getElementById('messageForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });

        // البحث في المحادثات
        document.getElementById('searchConversations').addEventListener('input', (e) => {
            this.searchConversations(e.target.value);
        });

        // البحث عن مستخدمين
        document.getElementById('userSearch').addEventListener('input', (e) => {
            this.searchUsers(e.target.value);
        });

        // Enter للإرسال
        document.getElementById('messageText').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
    }

    async loadConversations() {
        try {
            const response = await fetch('/api/v1/conversations', {
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.conversations = data.data.data || [];
                this.renderConversations();
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
        }
    }

    renderConversations() {
        const container = document.getElementById('conversationsList');
        
        if (this.conversations.length === 0) {
            container.innerHTML = `
                <div class="text-center p-4 text-muted">
                    <i class="ti ti-message-circle" style="font-size: 2rem;"></i>
                    <p class="mt-2">لا توجد محادثات بعد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.conversations.map(conversation => `
            <div class="conversation-item" data-id="${conversation.id}" onclick="messenger.openConversation(${conversation.id})">
                <div class="d-flex align-items-center">
                    <div class="position-relative me-3">
                        <img src="${conversation.participants[0]?.avatar || '/default-avatar.png'}" 
                             alt="" class="rounded-circle" width="50" height="50">
                        ${conversation.participants[0]?.is_online ? '<span class="online-indicator position-absolute bottom-0 end-0"></span>' : ''}
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${conversation.title}</h6>
                        <p class="mb-0 text-muted small">${conversation.last_message?.content || 'لا توجد رسائل'}</p>
                    </div>
                    <div class="text-end">
                        ${conversation.unread_count > 0 ? `<span class="badge bg-primary rounded-pill">${conversation.unread_count}</span>` : ''}
                        <small class="text-muted d-block">${this.formatTime(conversation.last_message_at)}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async openConversation(conversationId) {
        this.currentConversationId = conversationId;
        
        // تحديث UI
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-id="${conversationId}"]`).classList.add('active');

        // إظهار منطقة المحادثة
        document.getElementById('chatHeader').classList.remove('d-none');
        document.getElementById('chatInput').classList.remove('d-none');
        document.querySelector('.empty-chat').style.display = 'none';

        // تحميل الرسائل
        await this.loadMessages(conversationId);
        
        // تحديد المحادثة كمقروءة
        this.markConversationAsRead(conversationId);
    }

    async loadMessages(conversationId) {
        try {
            const response = await fetch(`/api/v1/conversations/${conversationId}/messages`, {
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.messages = data.data.data || [];
                this.renderMessages();
                this.scrollToBottom();
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }

    renderMessages() {
        const container = document.getElementById('chatMessages');
        
        container.innerHTML = this.messages.map(message => `
            <div class="message ${message.sender.id === this.getCurrentUserId() ? 'sent' : 'received'}">
                <div class="message-bubble">
                    ${message.content}
                    <div class="message-time">${this.formatTime(message.created_at)}</div>
                </div>
            </div>
        `).join('');
    }

    async sendMessage() {
        const messageText = document.getElementById('messageText');
        const content = messageText.value.trim();
        
        if (!content || !this.currentConversationId) return;

        try {
            const response = await fetch(`/api/v1/conversations/${this.currentConversationId}/messages/text`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content })
            });

            if (response.ok) {
                const data = await response.json();
                this.messages.push(data.data);
                this.renderMessages();
                this.scrollToBottom();
                messageText.value = '';
                
                // تحديث قائمة المحادثات
                this.loadConversations();
            }
        } catch (error) {
            console.error('Error sending message:', error);
        }
    }

    async searchUsers(query) {
        if (query.length < 2) {
            document.getElementById('searchResults').innerHTML = '';
            return;
        }

        try {
            const response = await fetch(`/api/v1/social/search-users?query=${encodeURIComponent(query)}`, {
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.renderSearchResults(data.data);
            }
        } catch (error) {
            console.error('Error searching users:', error);
        }
    }

    renderSearchResults(users) {
        const container = document.getElementById('searchResults');
        
        container.innerHTML = users.map(user => `
            <div class="user-search-item" onclick="messenger.startConversationWithUser(${user.id})">
                <img src="${user.avatar || '/default-avatar.png'}" alt="" class="rounded-circle" width="40" height="40">
                <div>
                    <h6 class="mb-0">${user.name}</h6>
                    <small class="text-muted">${user.email}</small>
                </div>
            </div>
        `).join('');
    }

    async startConversationWithUser(userId) {
        try {
            const response = await fetch('/api/v1/universal-chat/start-conversation', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ participant_id: userId })
            });

            if (response.ok) {
                const data = await response.json();
                
                // إغلاق المودال
                const modal = bootstrap.Modal.getInstance(document.getElementById('newChatModal'));
                modal.hide();
                
                // تحديث قائمة المحادثات
                await this.loadConversations();
                
                // فتح المحادثة الجديدة
                this.openConversation(data.data.id);
            }
        } catch (error) {
            console.error('Error starting conversation:', error);
        }
    }

    async markConversationAsRead(conversationId) {
        try {
            await fetch(`/api/v1/conversations/${conversationId}/mark-as-read`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Error marking conversation as read:', error);
        }
    }

    searchConversations(query) {
        const items = document.querySelectorAll('.conversation-item');
        items.forEach(item => {
            const title = item.querySelector('h6').textContent.toLowerCase();
            if (title.includes(query.toLowerCase())) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    setupWebSocket() {
        // إعداد WebSocket للتحديثات الفورية
        if (window.Echo) {
            // الاستماع للرسائل الجديدة
            window.Echo.private(`user.${this.getCurrentUserId()}`)
                .listen('MessageSent', (e) => {
                    if (e.message.conversation_id === this.currentConversationId) {
                        this.messages.push(e.message);
                        this.renderMessages();
                        this.scrollToBottom();
                    }
                    this.loadConversations(); // تحديث قائمة المحادثات
                });
        }
    }

    scrollToBottom() {
        const container = document.getElementById('chatMessages');
        container.scrollTop = container.scrollHeight;
    }

    formatTime(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const now = new Date();
        
        if (date.toDateString() === now.toDateString()) {
            return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('ar-SA');
        }
    }

    getCurrentUserId() {
        // يجب الحصول على معرف المستخدم الحالي من الخادم
        return window.currentUserId || 1;
    }
}

// تهيئة التطبيق
let messenger;
document.addEventListener('DOMContentLoaded', () => {
    messenger = new MessengerApp();
});
