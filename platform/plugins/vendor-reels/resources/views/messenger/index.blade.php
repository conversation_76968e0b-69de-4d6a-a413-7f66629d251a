@extends('core/base::layouts.master')

@section('content')
<div class="messenger-container">
    <div class="row h-100">
        <!-- قائمة المحادثات -->
        <div class="col-md-4 conversations-sidebar">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المحادثات</h5>
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#newChatModal">
                        <i class="ti ti-plus"></i> محادثة جديدة
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="search-box p-3 border-bottom">
                        <input type="text" class="form-control" placeholder="البحث في المحادثات..." id="searchConversations">
                    </div>
                    <div class="conversations-list" id="conversationsList">
                        <!-- سيتم تحميل المحادثات هنا عبر JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة المحادثة -->
        <div class="col-md-8 chat-area">
            <div class="card h-100">
                <div class="chat-header card-header d-none" id="chatHeader">
                    <div class="d-flex align-items-center">
                        <img src="" alt="" class="rounded-circle me-3" width="40" height="40" id="chatUserAvatar">
                        <div>
                            <h6 class="mb-0" id="chatUserName"></h6>
                            <small class="text-muted" id="chatUserStatus"></small>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="btn btn-outline-secondary btn-sm" title="معلومات المحادثة">
                            <i class="ti ti-info-circle"></i>
                        </button>
                    </div>
                </div>
                
                <div class="chat-messages card-body" id="chatMessages">
                    <div class="empty-chat text-center text-muted">
                        <i class="ti ti-message-circle" style="font-size: 4rem;"></i>
                        <h5>اختر محادثة للبدء</h5>
                        <p>اختر محادثة من القائمة أو ابدأ محادثة جديدة</p>
                    </div>
                </div>

                <div class="chat-input card-footer d-none" id="chatInput">
                    <form id="messageForm" class="d-flex align-items-center gap-2">
                        <button type="button" class="btn btn-outline-secondary" title="إرفاق ملف">
                            <i class="ti ti-paperclip"></i>
                        </button>
                        <div class="flex-grow-1">
                            <input type="text" class="form-control" placeholder="اكتب رسالتك..." id="messageText" autocomplete="off">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-send"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للمحادثة الجديدة -->
<div class="modal fade" id="newChatModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">بدء محادثة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">البحث عن مستخدم</label>
                    <input type="text" class="form-control" id="userSearch" placeholder="ابحث بالاسم أو البريد الإلكتروني">
                </div>
                <div id="searchResults"></div>
            </div>
        </div>
    </div>
</div>

<style>
.messenger-container {
    height: calc(100vh - 200px);
}

.conversations-sidebar {
    border-right: 1px solid #dee2e6;
}

.conversations-list {
    max-height: calc(100vh - 350px);
    overflow-y: auto;
}

.conversation-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.conversation-item:hover {
    background-color: #f8f9fa;
}

.conversation-item.active {
    background-color: #e3f2fd;
}

.chat-messages {
    height: calc(100vh - 350px);
    overflow-y: auto;
    padding: 20px;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.sent .message-bubble {
    background-color: #007bff;
    color: white;
}

.message.received .message-bubble {
    background-color: #f1f3f4;
    color: #333;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 5px;
}

.empty-chat {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.user-search-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-search-item:hover {
    background-color: #f8f9fa;
}

.online-indicator {
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    display: inline-block;
}
</style>
@endsection

@push('footer')
<script>
// JavaScript للمحادثات سيتم إضافته في ملف منفصل
</script>
@endpush
