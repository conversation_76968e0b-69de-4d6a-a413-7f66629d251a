<?php

use Illuminate\Support\Facades\Route;
use Botble\VendorReels\Http\Controllers\Api\ReelApiController;

/*
|--------------------------------------------------------------------------
| API Routes for Vendor Reels
|--------------------------------------------------------------------------
|
| Here are the API routes for the vendor reels functionality.
| These routes are used by mobile applications and external integrations.
|
*/

Route::group([
    'prefix' => 'v1/reels',
    'as' => 'api.reels.',
    'middleware' => ['api'],
], function () {
    
    // مسارات عامة (لا تتطلب مصادقة)
    Route::get('/', [ReelApiController::class, 'index'])->name('index');
    Route::get('/featured', [ReelApiController::class, 'featured'])->name('featured');
    Route::get('/popular', [ReelApiController::class, 'popular'])->name('popular');
    Route::get('/recent', [ReelApiController::class, 'recent'])->name('recent');
    Route::get('/search', [ReelApiController::class, 'search'])->name('search');
    Route::get('/store/{store}', [ReelApiController::class, 'byStore'])->name('by-store');
    Route::get('/{reel}', [ReelApiController::class, 'show'])->name('show');
    Route::get('/{reel}/comments', [ReelApiController::class, 'comments'])->name('comments');
    
    // تسجيل المشاهدة (لا يتطلب مصادقة)
    Route::post('/{reel}/view', [ReelApiController::class, 'recordView'])->name('view');
    
    // مسارات تتطلب مصادقة
    Route::group(['middleware' => ['auth:sanctum']], function () {
        Route::post('/{reel}/like', [ReelApiController::class, 'like'])->name('like');
        Route::delete('/{reel}/like', [ReelApiController::class, 'unlike'])->name('unlike');
        Route::post('/{reel}/comment', [ReelApiController::class, 'comment'])->name('comment');
        Route::post('/{reel}/save', [ReelApiController::class, 'save'])->name('save');
        Route::delete('/{reel}/save', [ReelApiController::class, 'unsave'])->name('unsave');
        Route::post('/{reel}/share', [ReelApiController::class, 'share'])->name('share');
    });
});

/*
|--------------------------------------------------------------------------
| Messaging API Routes
|--------------------------------------------------------------------------
|
| Routes for the messaging system (conversations and messages)
|
*/

Route::group([
    'prefix' => 'v1/conversations',
    'as' => 'api.conversations.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {

    // مسارات المحادثات
    Route::get('/', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'index'])->name('index');
    Route::post('/', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'store'])->name('store');
    Route::post('/start-with-store', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'startWithStore'])->name('start-with-store');
    Route::get('/{conversation}', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'show'])->name('show');
    Route::post('/{conversation}/mark-as-read', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'markAsRead'])->name('mark-as-read');
    Route::post('/{conversation}/add-participant', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'addParticipant'])->name('add-participant');
    Route::post('/{conversation}/leave', [\Botble\VendorReels\Http\Controllers\Api\ConversationApiController::class, 'leave'])->name('leave');

    // مسارات الرسائل
    Route::get('/{conversation}/messages', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'index'])->name('messages.index');
    Route::post('/{conversation}/messages/text', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'sendText'])->name('messages.send-text');
    Route::post('/{conversation}/messages/media', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'sendMedia'])->name('messages.send-media');
    Route::post('/{conversation}/messages/reel', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'sendReel'])->name('messages.send-reel');
    Route::post('/{conversation}/messages/product', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'sendProduct'])->name('messages.send-product');
    Route::post('/{conversation}/messages/location', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'sendLocation'])->name('messages.send-location');
});

Route::group([
    'prefix' => 'v1/messages',
    'as' => 'api.messages.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {
    Route::put('/{message}/edit', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'edit'])->name('edit');
    Route::delete('/{message}', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'delete'])->name('delete');
    Route::post('/{message}/mark-as-read', [\Botble\VendorReels\Http\Controllers\Api\MessageApiController::class, 'markAsRead'])->name('mark-as-read');
});

/*
|--------------------------------------------------------------------------
| Social Features API Routes
|--------------------------------------------------------------------------
|
| Routes for social features (follow, stories, notifications)
|
*/

Route::group([
    'prefix' => 'v1/social',
    'as' => 'api.social.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {

    // متابعة المستخدمين
    Route::post('/follow', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'follow'])->name('follow');
    Route::post('/unfollow', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'unfollow'])->name('unfollow');
    Route::get('/users/{userId}/followers', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'followers'])->name('followers');
    Route::get('/users/{userId}/following', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'following'])->name('following');
    Route::get('/users/{userId}/profile', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'getUserProfile'])->name('user-profile');
    Route::get('/search-users', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'searchUsers'])->name('search-users');

    // الإشعارات
    Route::get('/notifications', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'getNotifications'])->name('notifications');
    Route::post('/notifications/{notificationId}/read', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'markNotificationAsRead'])->name('notification-read');
    Route::post('/notifications/read-all', [\Botble\VendorReels\Http\Controllers\Api\SocialApiController::class, 'markAllNotificationsAsRead'])->name('notifications-read-all');
});

/*
|--------------------------------------------------------------------------
| Stories API Routes
|--------------------------------------------------------------------------
|
| Routes for user stories/status updates
|
*/

Route::group([
    'prefix' => 'v1/stories',
    'as' => 'api.stories.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {

    Route::get('/', [\Botble\VendorReels\Http\Controllers\Api\StoryApiController::class, 'index'])->name('index');
    Route::post('/', [\Botble\VendorReels\Http\Controllers\Api\StoryApiController::class, 'store'])->name('store');
    Route::get('/{story}', [\Botble\VendorReels\Http\Controllers\Api\StoryApiController::class, 'show'])->name('show');
    Route::delete('/{story}', [\Botble\VendorReels\Http\Controllers\Api\StoryApiController::class, 'destroy'])->name('destroy');
    Route::get('/{story}/viewers', [\Botble\VendorReels\Http\Controllers\Api\StoryApiController::class, 'viewers'])->name('viewers');
    Route::get('/users/{userId}', [\Botble\VendorReels\Http\Controllers\Api\StoryApiController::class, 'userStories'])->name('user-stories');
});

/*
|--------------------------------------------------------------------------
| Universal Chat API Routes
|--------------------------------------------------------------------------
|
| Routes for universal chat system supporting all user types
|
*/

Route::group([
    'prefix' => 'v1/universal-chat',
    'as' => 'api.universal-chat.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {

    Route::post('/start-conversation', [\Botble\VendorReels\Http\Controllers\Api\UniversalChatApiController::class, 'startConversation'])->name('start-conversation');
    Route::get('/online-users', [\Botble\VendorReels\Http\Controllers\Api\UniversalChatApiController::class, 'getOnlineUsers'])->name('online-users');
    Route::get('/suggested-users', [\Botble\VendorReels\Http\Controllers\Api\UniversalChatApiController::class, 'getSuggestedUsers'])->name('suggested-users');
});

/*
|--------------------------------------------------------------------------
| User Management API Routes
|--------------------------------------------------------------------------
|
| Routes for user blocking, close friends, etc.
|
*/

Route::group([
    'prefix' => 'v1/user-management',
    'as' => 'api.user-management.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {

    // حظر المستخدمين
    Route::post('/block', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'blockUser'])->name('block');
    Route::post('/unblock', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'unblockUser'])->name('unblock');
    Route::get('/blocked-users', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'getBlockedUsers'])->name('blocked-users');

    // الأصدقاء المقربين
    Route::post('/close-friends/add', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'addCloseFriend'])->name('close-friends.add');
    Route::post('/close-friends/remove', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'removeCloseFriend'])->name('close-friends.remove');
    Route::get('/close-friends', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'getCloseFriends'])->name('close-friends');

    // حالة الاتصال
    Route::post('/update-online-status', [\Botble\VendorReels\Http\Controllers\Api\UserManagementApiController::class, 'updateOnlineStatus'])->name('update-online-status');
});

// مسارات API للبائعين (سيتم إضافتها لاحقاً)
// Route::group([
//     'prefix' => 'v1/vendor/reels',
//     'as' => 'api.vendor.reels.',
//     'middleware' => ['api', 'auth:sanctum', 'vendor'],
// ], function () {
//     // سيتم إضافة مسارات البائعين هنا لاحقاً
// });
