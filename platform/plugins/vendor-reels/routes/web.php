<?php

use Illuminate\Support\Facades\Route;
use Botble\VendorReels\Http\Controllers\PublicReelController;
use Botble\VendorReels\Http\Controllers\ReelInteractionController;

// مسار اختبار مباشر
Route::get('test-reels-direct', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()->with('store')->paginate(12);
        $featuredReels = \Botble\VendorReels\Models\VendorReel::published()->featured()->with('store')->limit(6)->get();
        $popularReels = \Botble\VendorReels\Models\VendorReel::published()->with('store')->orderBy('views_count', 'desc')->limit(6)->get();
        $stores = \Botble\Marketplace\Models\Store::whereHas('reels', function($query) {
            $query->where('status', 'published');
        })->limit(10)->get();

        return view('plugins/vendor-reels::public.index', compact('reels', 'featuredReels', 'popularReels', 'stores'));
    } catch (Exception $e) {
        return response()->json(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()], 500);
    }
})->name('test.reels.direct');

// مسار اختبار بسيط
Route::get('test-reels-simple', function() {
    try {
        $reelsCount = \Botble\VendorReels\Models\VendorReel::count();
        $publishedCount = \Botble\VendorReels\Models\VendorReel::published()->count();
        $stores = \Botble\Marketplace\Models\Store::whereHas('reels')->count();

        return response()->json([
            'status' => 'working',
            'plugin_active' => true,
            'total_reels' => $reelsCount,
            'published_reels' => $publishedCount,
            'stores_with_reels' => $stores,
            'timestamp' => now()->toISOString(),
            'routes_registered' => true,
            'database_connected' => true,
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('test.reels.simple');

// مسار اختبار للصفحة العامة
Route::get('test-reels-public', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()->with('store')->paginate(12);
        $featuredReels = \Botble\VendorReels\Models\VendorReel::published()->featured()->with('store')->limit(6)->get();
        $popularReels = \Botble\VendorReels\Models\VendorReel::published()->with('store')->orderBy('views_count', 'desc')->limit(6)->get();

        return response()->json([
            'status' => 'working',
            'reels_count' => $reels->total(),
            'featured_count' => $featuredReels->count(),
            'popular_count' => $popularReels->count(),
            'reels_sample' => $reels->take(2)->map(function($reel) {
                return [
                    'id' => $reel->id,
                    'title' => $reel->title,
                    'store' => $reel->store->name ?? 'Unknown',
                    'views' => $reel->views_count,
                    'likes' => $reel->likes_count,
                ];
            }),
            'timestamp' => now()->toISOString(),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('test.reels.public');

// مسار اختبار للصفحة العامة مع HTML
Route::get('test-reels-html', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()->with('store')->paginate(12);
        $featuredReels = \Botble\VendorReels\Models\VendorReel::published()->featured()->with('store')->limit(6)->get();
        $popularReels = \Botble\VendorReels\Models\VendorReel::published()->with('store')->orderBy('views_count', 'desc')->limit(6)->get();

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الريلز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
        .reel-card { border-radius: 12px; overflow: hidden; transition: transform 0.3s ease; }
        .reel-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">🎬 اختبار نظام الريلز</h1>

        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي الريلز</h5>
                        <h2 class="text-primary">' . $reels->total() . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">الريلز المميزة</h5>
                        <h2 class="text-success">' . $featuredReels->count() . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">الريلز الشائعة</h5>
                        <h2 class="text-warning">' . $popularReels->count() . '</h2>
                    </div>
                </div>
            </div>
        </div>';

        if ($reels->count() > 0) {
            $html .= '<h3 class="mb-4">🌟 الريلز المتاحة</h3><div class="row">';
            foreach ($reels as $reel) {
                $html .= '
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card reel-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">' . $reel->title . '</h5>
                            <p class="card-text text-muted">' . ($reel->description ?: 'لا يوجد وصف') . '</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">👁️ ' . $reel->views_count . ' مشاهدة</small>
                                <small class="text-muted">❤️ ' . $reel->likes_count . ' إعجاب</small>
                            </div>
                            <div class="mt-2">
                                <span class="badge bg-primary">' . ($reel->store->name ?? 'متجر غير معروف') . '</span>
                                ' . ($reel->is_featured ? '<span class="badge bg-warning">مميز</span>' : '') . '
                            </div>
                        </div>
                    </div>
                </div>';
            }
            $html .= '</div>';
        } else {
            $html .= '<div class="alert alert-info text-center">لا توجد ريلز متاحة حالياً</div>';
        }

        $html .= '
        <div class="mt-5 text-center">
            <a href="/test-reels-simple" class="btn btn-primary me-2">اختبار API</a>
            <a href="/v1/reels" class="btn btn-success">API الريلز</a>
        </div>
    </div>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('test.reels.html');

// مسار اختبار لصفحات البائعين
Route::get('test-vendor-reels', function() {
    try {
        $customersCount = \Botble\Ecommerce\Models\Customer::count();
        $vendorsCount = \Botble\Ecommerce\Models\Customer::where('is_vendor', true)->count();
        $storesCount = \Botble\Marketplace\Models\Store::count();
        $publishedStoresCount = \Botble\Marketplace\Models\Store::where('status', 'published')->count();

        // إحصائيات الريلز حسب المتاجر
        $storesWithReels = \Botble\Marketplace\Models\Store::whereHas('reels')->with(['reels' => function($query) {
            $query->select('id', 'store_id', 'title', 'status', 'views_count', 'likes_count');
        }])->get();

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحات البائعين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">👨‍💼 اختبار صفحات البائعين</h1>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">العملاء</h5>
                        <h2 class="text-primary">' . $customersCount . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">البائعين</h5>
                        <h2 class="text-success">' . $vendorsCount . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">المتاجر</h5>
                        <h2 class="text-warning">' . $storesCount . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">المتاجر المفعلة</h5>
                        <h2 class="text-info">' . $publishedStoresCount . '</h2>
                    </div>
                </div>
            </div>
        </div>';

        if ($storesWithReels->count() > 0) {
            $html .= '<h3 class="mb-4">🏪 المتاجر التي لديها ريلز</h3>';
            foreach ($storesWithReels as $store) {
                $html .= '
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">' . $store->name . ' <span class="badge bg-' . ($store->status === 'published' ? 'success' : 'warning') . '">' . $store->status . '</span></h5>
                    </div>
                    <div class="card-body">
                        <p><strong>البريد الإلكتروني:</strong> ' . ($store->email ?: 'غير محدد') . '</p>
                        <p><strong>الهاتف:</strong> ' . ($store->phone ?: 'غير محدد') . '</p>
                        <p><strong>عدد الريلز:</strong> ' . $store->reels->count() . '</p>';

                if ($store->reels->count() > 0) {
                    $html .= '<h6>الريلز:</h6><ul>';
                    foreach ($store->reels as $reel) {
                        $html .= '<li>' . $reel->title . ' (👁️ ' . $reel->views_count . ' - ❤️ ' . $reel->likes_count . ') - <span class="badge bg-' . ($reel->status === 'published' ? 'success' : 'secondary') . '">' . $reel->status . '</span></li>';
                    }
                    $html .= '</ul>';
                }

                $html .= '</div></div>';
            }
        } else {
            $html .= '<div class="alert alert-info text-center">لا توجد متاجر لديها ريلز حالياً</div>';
        }

        // اختبار مسارات البائعين
        $html .= '
        <h3 class="mb-4 mt-5">🔗 اختبار مسارات البائعين</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">مسارات الريلز للبائعين</div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">GET /vendor/reels - قائمة الريلز</li>
                            <li class="list-group-item">GET /vendor/reels/create - إنشاء ريل جديد</li>
                            <li class="list-group-item">POST /vendor/reels - حفظ ريل جديد</li>
                            <li class="list-group-item">GET /vendor/reels/{id}/edit - تعديل ريل</li>
                            <li class="list-group-item">PUT /vendor/reels/{id} - تحديث ريل</li>
                            <li class="list-group-item">DELETE /vendor/reels/{id} - حذف ريل</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">مسارات AJAX</div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">GET /vendor/ajax/reels/products - منتجات المتجر</li>
                            <li class="list-group-item">GET /vendor/ajax/reels/stats - إحصائيات سريعة</li>
                            <li class="list-group-item">POST /vendor/reels/upload-video - رفع فيديو</li>
                            <li class="list-group-item">POST /vendor/reels/upload-thumbnail - رفع صورة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-5 text-center">
            <a href="/test-reels-html" class="btn btn-primary me-2">اختبار الصفحة العامة</a>
            <a href="/test-reels-simple" class="btn btn-success">اختبار API</a>
        </div>
    </div>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('test.vendor.reels');

// مسار اختبار رفع الملفات
Route::get('test-upload-reels', function() {
    try {
        // فحص إعدادات الرفع
        $maxVideoSize = vendor_reels_config('video.max_size', 104857600);
        $maxThumbnailSize = vendor_reels_config('thumbnail.max_size', 5242880);
        $allowedVideoFormats = vendor_reels_config('video.allowed_formats', ['mp4', 'mov', 'avi']);
        $allowedThumbnailFormats = vendor_reels_config('thumbnail.allowed_formats', ['jpg', 'jpeg', 'png', 'gif']);

        // فحص مجلدات التخزين
        $uploadsPath = storage_path('app/public');
        $reelsPath = $uploadsPath . '/vendor-reels';
        $videosPath = $reelsPath . '/videos';
        $thumbnailsPath = $reelsPath . '/thumbnails';

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الملفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">📁 اختبار رفع الملفات</h1>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">إعدادات الفيديو</div>
                    <div class="card-body">
                        <p><strong>الحد الأقصى للحجم:</strong> ' . number_format($maxVideoSize / 1024 / 1024, 2) . ' MB</p>
                        <p><strong>الصيغ المدعومة:</strong> ' . implode(', ', $allowedVideoFormats) . '</p>
                        <p><strong>الحد الأقصى للمدة:</strong> ' . vendor_reels_config('video.max_duration', 300) . ' ثانية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">إعدادات الصورة المصغرة</div>
                    <div class="card-body">
                        <p><strong>الحد الأقصى للحجم:</strong> ' . number_format($maxThumbnailSize / 1024 / 1024, 2) . ' MB</p>
                        <p><strong>الصيغ المدعومة:</strong> ' . implode(', ', $allowedThumbnailFormats) . '</p>
                        <p><strong>الأبعاد المقترحة:</strong> 600x800 بكسل</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">حالة مجلدات التخزين</div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                مجلد الرفع الرئيسي
                                <span class="badge bg-' . (is_dir($uploadsPath) && is_writable($uploadsPath) ? 'success">متاح' : 'danger">غير متاح') . '</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                مجلد الريلز
                                <span class="badge bg-' . (is_dir($reelsPath) ? 'success">موجود' : 'warning">غير موجود') . '</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                مجلد الفيديوهات
                                <span class="badge bg-' . (is_dir($videosPath) ? 'success">موجود' : 'warning">غير موجود') . '</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                مجلد الصور المصغرة
                                <span class="badge bg-' . (is_dir($thumbnailsPath) ? 'success">موجود' : 'warning">غير موجود') . '</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">إعدادات PHP للرفع</div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                upload_max_filesize
                                <span class="badge bg-info">' . ini_get('upload_max_filesize') . '</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                post_max_size
                                <span class="badge bg-info">' . ini_get('post_max_size') . '</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                max_execution_time
                                <span class="badge bg-info">' . ini_get('max_execution_time') . ' ثانية</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                memory_limit
                                <span class="badge bg-info">' . ini_get('memory_limit') . '</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">مسارات رفع الفيديو</div>
                    <div class="card-body">
                        <p><strong>POST</strong> /vendor/reels/upload-video</p>
                        <p><small class="text-muted">يتطلب ملف بمعامل "video"</small></p>
                        <p><strong>المعاملات المطلوبة:</strong></p>
                        <ul>
                            <li>video (file): ملف الفيديو</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">مسارات رفع الصورة</div>
                    <div class="card-body">
                        <p><strong>POST</strong> /vendor/reels/upload-thumbnail</p>
                        <p><small class="text-muted">يتطلب ملف بمعامل "thumbnail"</small></p>
                        <p><strong>المعاملات المطلوبة:</strong></p>
                        <ul>
                            <li>thumbnail (file): ملف الصورة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-5 text-center">
            <a href="/test-vendor-reels" class="btn btn-primary me-2">اختبار البائعين</a>
            <a href="/test-reels-html" class="btn btn-success">اختبار الصفحة العامة</a>
        </div>
    </div>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('test.upload.reels');

// صفحة اختبار شاملة
Route::get('test-reels-dashboard', function() {
    try {
        // جمع جميع الإحصائيات
        $stats = [
            'reels' => [
                'total' => \Botble\VendorReels\Models\VendorReel::count(),
                'published' => \Botble\VendorReels\Models\VendorReel::published()->count(),
                'featured' => \Botble\VendorReels\Models\VendorReel::published()->featured()->count(),
                'draft' => \Botble\VendorReels\Models\VendorReel::where('status', 'draft')->count(),
            ],
            'interactions' => [
                'total' => \Botble\VendorReels\Models\ReelInteraction::count(),
                'likes' => \Botble\VendorReels\Models\ReelInteraction::where('type', 'like')->count(),
                'comments' => \Botble\VendorReels\Models\ReelInteraction::where('type', 'comment')->count(),
                'views' => \Botble\VendorReels\Models\ReelInteraction::where('type', 'view')->count(),
            ],
            'stores' => [
                'total' => \Botble\Marketplace\Models\Store::count(),
                'with_reels' => \Botble\Marketplace\Models\Store::whereHas('reels')->count(),
                'published' => \Botble\Marketplace\Models\Store::where('status', 'published')->count(),
            ],
            'customers' => [
                'total' => \Botble\Ecommerce\Models\Customer::count(),
                'vendors' => \Botble\Ecommerce\Models\Customer::where('is_vendor', true)->count(),
            ]
        ];

        // اختبار APIs
        $apiTests = [];
        try {
            $controller = new \Botble\VendorReels\Http\Controllers\Api\ReelApiController(app(\Botble\VendorReels\Repositories\Interfaces\VendorReelInterface::class));
            $request = new \Illuminate\Http\Request();

            $response = $controller->index($request);
            $apiTests['index'] = ['status' => $response->getStatusCode(), 'success' => $response->getStatusCode() === 200];

            $response = $controller->featured($request);
            $apiTests['featured'] = ['status' => $response->getStatusCode(), 'success' => $response->getStatusCode() === 200];

            $response = $controller->popular($request);
            $apiTests['popular'] = ['status' => $response->getStatusCode(), 'success' => $response->getStatusCode() === 200];

            $response = $controller->recent($request);
            $apiTests['recent'] = ['status' => $response->getStatusCode(), 'success' => $response->getStatusCode() === 200];

        } catch (\Exception $e) {
            $apiTests['error'] = $e->getMessage();
        }

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم اختبار الريلز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stat-card { transition: transform 0.3s ease; }
        .stat-card:hover { transform: translateY(-5px); }
        .test-success { color: #28a745; }
        .test-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="text-center mb-5">🎬 لوحة تحكم اختبار نظام الريلز</h1>

        <!-- إحصائيات عامة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-video fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">الريلز</h5>
                        <h2 class="text-primary">' . $stats['reels']['total'] . '</h2>
                        <small class="text-muted">' . $stats['reels']['published'] . ' منشور | ' . $stats['reels']['featured'] . ' مميز</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                        <h5 class="card-title">التفاعلات</h5>
                        <h2 class="text-danger">' . $stats['interactions']['total'] . '</h2>
                        <small class="text-muted">' . $stats['interactions']['likes'] . ' إعجاب | ' . $stats['interactions']['comments'] . ' تعليق</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-store fa-2x text-success mb-2"></i>
                        <h5 class="card-title">المتاجر</h5>
                        <h2 class="text-success">' . $stats['stores']['total'] . '</h2>
                        <small class="text-muted">' . $stats['stores']['with_reels'] . ' لديها ريلز | ' . $stats['stores']['published'] . ' مفعل</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">العملاء</h5>
                        <h2 class="text-warning">' . $stats['customers']['total'] . '</h2>
                        <small class="text-muted">' . $stats['customers']['vendors'] . ' بائع</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبارات API -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-code"></i> اختبارات API</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">';

        foreach ($apiTests as $test => $result) {
            if ($test === 'error') {
                $html .= '<div class="col-12"><div class="alert alert-danger">خطأ في API: ' . $result . '</div></div>';
            } else {
                $icon = $result['success'] ? 'fas fa-check-circle test-success' : 'fas fa-times-circle test-error';
                $badge = $result['success'] ? 'success' : 'danger';
                $html .= '
                <div class="col-md-3 mb-2">
                    <div class="d-flex align-items-center">
                        <i class="' . $icon . ' me-2"></i>
                        <span>' . ucfirst($test) . '</span>
                        <span class="badge bg-' . $badge . ' ms-auto">' . $result['status'] . '</span>
                    </div>
                </div>';
            }
        }

        $html .= '
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط الاختبار -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">اختبارات الصفحات العامة</div>
                    <div class="card-body">
                        <a href="/test-reels-simple" class="btn btn-primary btn-sm mb-2 w-100">اختبار API بسيط</a>
                        <a href="/test-reels-public" class="btn btn-success btn-sm mb-2 w-100">اختبار الصفحة العامة</a>
                        <a href="/test-reels-html" class="btn btn-info btn-sm mb-2 w-100">عرض HTML للريلز</a>
                        <a href="/reels-instagram" class="btn btn-warning btn-sm mb-2 w-100">🎬 نمط Instagram</a>
                        <a href="/reels-instagram-local" class="btn btn-success btn-sm mb-2 w-100">🎬 نمط Instagram (محسن)</a>
                        <a href="/reels-standalone" class="btn btn-dark btn-sm mb-2 w-100">🎬 نمط Instagram (مستقل)</a>
                        <a href="/reels-fixed" class="btn btn-primary btn-sm w-100">🎬 نمط Instagram (محدث) ⭐</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">اختبارات البائعين</div>
                    <div class="card-body">
                        <a href="/test-vendor-reels" class="btn btn-warning btn-sm mb-2 w-100">صفحة البائعين</a>
                        <a href="/test-upload-reels" class="btn btn-secondary btn-sm mb-2 w-100">اختبار رفع الملفات</a>
                        <a href="/vendor/reels" class="btn btn-dark btn-sm w-100">لوحة تحكم البائع</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">APIs مباشرة</div>
                    <div class="card-body">
                        <a href="/v1/reels" class="btn btn-outline-primary btn-sm mb-2 w-100" target="_blank">API الريلز</a>
                        <a href="/v1/reels/featured" class="btn btn-outline-success btn-sm mb-2 w-100" target="_blank">الريلز المميزة</a>
                        <a href="/v1/reels/popular" class="btn btn-outline-warning btn-sm w-100" target="_blank">الريلز الشائعة</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>إعدادات الريلز</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الحد الأقصى لحجم الفيديو:</strong> ' . number_format(vendor_reels_config('video.max_size', 104857600) / 1024 / 1024, 2) . ' MB</li>
                                    <li><strong>الحد الأقصى للمدة:</strong> ' . vendor_reels_config('video.max_duration', 300) . ' ثانية</li>
                                    <li><strong>الريلز اليومية للبائع:</strong> ' . vendor_reels_config('security.max_reels_per_day', 10) . '</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>حالة النظام</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Plugin مفعل:</strong> <span class="badge bg-success">نعم</span></li>
                                    <li><strong>قاعدة البيانات:</strong> <span class="badge bg-success">متصلة</span></li>
                                    <li><strong>مجلدات التخزين:</strong> <span class="badge bg-success">جاهزة</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="/reels-styles-demo" class="btn btn-lg btn-gradient me-3" style="background: linear-gradient(45deg, #833ab4 0%, #fd1d1d 50%, #fcb045 100%); color: white; border: none;">
                <i class="fab fa-instagram me-2"></i>مقارنة أنماط العرض
            </a>
            <p class="text-muted mt-3">آخر تحديث: ' . now()->format('Y-m-d H:i:s') . '</p>
        </div>
    </div>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('test.reels.dashboard');

// صفحة الريلز بنمط Instagram
Route::get('reels-instagram', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('plugins/vendor-reels::public.instagram-style', compact('reels'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('public.reels.instagram');

// صفحة الريلز بنمط Instagram (نسخة محسنة)
Route::get('reels-instagram-local', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('plugins/vendor-reels::public.instagram-local', compact('reels'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('public.reels.instagram.local');

// صفحة الريلز بنمط Instagram (مستقلة تماماً)
Route::get('reels-standalone', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="' . csrf_token() . '">
    <title>الريلز - نمط Instagram</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .reels-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .reel-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.3s ease;
        }

        .reel-item.active {
            opacity: 1;
            transform: translateY(0);
        }

        .reel-item.prev {
            transform: translateY(-100%);
        }

        .reel-item.next {
            transform: translateY(100%);
        }

        .reel-video-container {
            position: relative;
            width: 100%;
            height: 100%;
            max-width: 400px;
            background: #000;
            border-radius: 0;
            overflow: hidden;
        }

        .reel-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #000;
        }

        .reel-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent 60%, rgba(0,0,0,0.8));
            pointer-events: none;
        }

        .reel-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            color: white;
            z-index: 10;
        }

        .reel-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .reel-description {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .reel-store {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .store-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #333;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .store-name {
            font-size: 14px;
            font-weight: 500;
        }

        .reel-actions {
            position: absolute;
            right: 15px;
            bottom: 80px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            z-index: 10;
        }

        .action-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .action-btn.liked {
            background: #ff3040;
        }

        .action-count {
            font-size: 12px;
            text-align: center;
            margin-top: 4px;
            font-weight: 500;
        }

        .reel-progress {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            height: 2px;
            background: rgba(255,255,255,0.3);
            border-radius: 1px;
            z-index: 10;
        }

        .progress-bar {
            height: 100%;
            background: white;
            border-radius: 1px;
            width: 0%;
            transition: width 0.1s linear;
        }

        .navigation-hint {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255,255,255,0.7);
            font-size: 14px;
            text-align: center;
            z-index: 5;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .reels-container:hover .navigation-hint {
            opacity: 1;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(0,0,0,0.5);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            z-index: 20;
            backdrop-filter: blur(10px);
        }

        .reel-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }

        .success-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .success-message.show {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .reel-video-container {
                max-width: 100%;
                border-radius: 0;
            }

            .reel-content {
                padding: 15px;
            }

            .reel-actions {
                right: 10px;
                bottom: 60px;
                gap: 15px;
            }

            .action-btn {
                width: 44px;
                height: 44px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="reels-container" id="reelsContainer">';

        if ($reels->count() > 0) {
            foreach ($reels as $index => $reel) {
                $activeClass = $index === 0 ? 'active' : '';
                $html .= '
                <div class="reel-item ' . $activeClass . '" data-reel-id="' . $reel->id . '" data-index="' . $index . '">
                    <div class="reel-video-container">';

                if ($reel->video_url) {
                    $html .= '
                        <video
                            class="reel-video"
                            src="' . $reel->video_url . '"
                            loop
                            muted
                            playsinline
                            preload="metadata"
                            data-reel-video="' . $reel->id . '"
                        ></video>';
                } else {
                    $html .= '
                        <div class="reel-placeholder">
                            <span>▶</span>
                        </div>';
                }

                $html .= '
                        <div class="reel-overlay"></div>

                        <div class="reel-progress">
                            <div class="progress-bar" data-progress="' . $reel->id . '"></div>
                        </div>

                        <div class="reel-content">
                            <div class="reel-store">
                                <div class="store-avatar">
                                    ' . substr($reel->store->name ?? 'S', 0, 1) . '
                                </div>
                                <div class="store-name">' . ($reel->store->name ?? 'متجر غير معروف') . '</div>
                            </div>

                            <h3 class="reel-title">' . $reel->title . '</h3>';

                if ($reel->description) {
                    $html .= '<p class="reel-description">' . \Illuminate\Support\Str::limit($reel->description, 100) . '</p>';
                }

                $html .= '
                        </div>

                        <div class="reel-actions">
                            <div>
                                <button class="action-btn like-btn" data-reel-id="' . $reel->id . '">
                                    ♥
                                </button>
                                <div class="action-count">' . format_views_count($reel->likes_count) . '</div>
                            </div>

                            <div>
                                <button class="action-btn comment-btn" data-reel-id="' . $reel->id . '">
                                    💬
                                </button>
                                <div class="action-count">' . format_views_count($reel->comments_count) . '</div>
                            </div>

                            <div>
                                <button class="action-btn share-btn" data-reel-id="' . $reel->id . '">
                                    📤
                                </button>
                                <div class="action-count">' . format_views_count($reel->shares_count) . '</div>
                            </div>

                            <div>
                                <button class="action-btn save-btn" data-reel-id="' . $reel->id . '">
                                    🔖
                                </button>
                            </div>
                        </div>
                    </div>
                </div>';
            }
        } else {
            $html .= '
            <div class="reel-item active">
                <div class="reel-video-container">
                    <div class="reel-placeholder">
                        <div style="text-align: center; color: white;">
                            <div style="font-size: 64px; margin-bottom: 20px;">📹</div>
                            <h3>لا توجد ريلز متاحة</h3>
                            <p>تحقق مرة أخرى لاحقاً</p>
                        </div>
                    </div>
                </div>
            </div>';
        }

        $html .= '
        <button class="close-btn" onclick="window.history.back()">
            ✕
        </button>

        <div class="navigation-hint">
            <div>اسحب لأعلى أو لأسفل للتنقل</div>
            <div style="margin-top: 5px; font-size: 12px;">أو استخدم الأسهم ↑ ↓</div>
        </div>

        <div class="success-message" id="successMessage"></div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const container = document.getElementById("reelsContainer");
            const reelItems = document.querySelectorAll(".reel-item");
            const successMessage = document.getElementById("successMessage");
            let currentIndex = 0;
            let isTransitioning = false;

            if (reelItems.length > 0) {
                playCurrentVideo();
            }

            function showMessage(text) {
                successMessage.textContent = text;
                successMessage.classList.add("show");
                setTimeout(() => {
                    successMessage.classList.remove("show");
                }, 2000);
            }

            function goToNext() {
                if (isTransitioning || currentIndex >= reelItems.length - 1) return;

                isTransitioning = true;

                reelItems[currentIndex].classList.remove("active");
                reelItems[currentIndex].classList.add("prev");

                currentIndex++;

                reelItems[currentIndex].classList.remove("next");
                reelItems[currentIndex].classList.add("active");

                playCurrentVideo();
                recordView(getCurrentReelId());

                setTimeout(() => {
                    isTransitioning = false;
                }, 300);
            }

            function goToPrev() {
                if (isTransitioning || currentIndex <= 0) return;

                isTransitioning = true;

                reelItems[currentIndex].classList.remove("active");
                reelItems[currentIndex].classList.add("next");

                currentIndex--;

                reelItems[currentIndex].classList.remove("prev");
                reelItems[currentIndex].classList.add("active");

                playCurrentVideo();
                recordView(getCurrentReelId());

                setTimeout(() => {
                    isTransitioning = false;
                }, 300);
            }

            function playCurrentVideo() {
                document.querySelectorAll(".reel-video").forEach(video => {
                    video.pause();
                    video.currentTime = 0;
                });

                const currentVideo = reelItems[currentIndex].querySelector(".reel-video");
                if (currentVideo) {
                    currentVideo.play().catch(e => console.log("Video play failed:", e));
                    updateProgress(currentVideo);
                }
            }

            function updateProgress(video) {
                const progressBar = video.parentElement.querySelector(".progress-bar");
                if (!progressBar) return;

                const updateProgressBar = () => {
                    if (video.duration) {
                        const progress = (video.currentTime / video.duration) * 100;
                        progressBar.style.width = progress + "%";
                    }
                };

                video.addEventListener("timeupdate", updateProgressBar);
                video.addEventListener("ended", () => {
                    progressBar.style.width = "100%";
                    setTimeout(goToNext, 500);
                });
            }

            function getCurrentReelId() {
                return reelItems[currentIndex].dataset.reelId;
            }

            // Touch/Swipe handling
            let startY = 0;
            let startTime = 0;

            container.addEventListener("touchstart", (e) => {
                startY = e.touches[0].clientY;
                startTime = Date.now();
            });

            container.addEventListener("touchend", (e) => {
                const endY = e.changedTouches[0].clientY;
                const endTime = Date.now();
                const deltaY = startY - endY;
                const deltaTime = endTime - startTime;

                if (Math.abs(deltaY) > 50 && deltaTime < 300) {
                    if (deltaY > 0) {
                        goToNext();
                    } else {
                        goToPrev();
                    }
                }
            });

            // Keyboard navigation
            document.addEventListener("keydown", (e) => {
                switch(e.key) {
                    case "ArrowUp":
                        e.preventDefault();
                        goToPrev();
                        break;
                    case "ArrowDown":
                        e.preventDefault();
                        goToNext();
                        break;
                    case "Escape":
                        window.history.back();
                        break;
                }
            });

            // Mouse wheel navigation
            container.addEventListener("wheel", (e) => {
                e.preventDefault();
                if (e.deltaY > 0) {
                    goToNext();
                } else {
                    goToPrev();
                }
            });

            // Action buttons
            document.querySelectorAll(".like-btn").forEach(btn => {
                btn.addEventListener("click", function() {
                    const reelId = this.dataset.reelId;
                    toggleLike(reelId, this);
                });
            });

            function recordView(reelId) {
                fetch(`/ajax/reels/${reelId}/view`, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                        "Content-Type": "application/json",
                    }
                }).catch(e => console.log("View recording failed:", e));
            }

            function toggleLike(reelId, button) {
                const isLiked = button.classList.contains("liked");
                const url = isLiked ? `/ajax/reels/${reelId}/unlike` : `/ajax/reels/${reelId}/like`;
                const method = isLiked ? "DELETE" : "POST";

                fetch(url, {
                    method: method,
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                        "Content-Type": "application/json",
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        button.classList.toggle("liked");
                        const countElement = button.parentElement.querySelector(".action-count");
                        if (countElement && data.likes_count !== undefined) {
                            countElement.textContent = formatCount(data.likes_count);
                        }
                        showMessage(isLiked ? "تم إلغاء الإعجاب" : "تم الإعجاب!");
                    } else {
                        showMessage(data.error || "حدث خطأ");
                    }
                })
                .catch(e => {
                    console.log("Like toggle failed:", e);
                    showMessage("حدث خطأ في الاتصال");
                });
            }

            function formatCount(count) {
                if (count >= 1000000) {
                    return (count / 1000000).toFixed(1) + "M";
                } else if (count >= 1000) {
                    return (count / 1000).toFixed(1) + "K";
                }
                return count.toString();
            }

            if (reelItems.length > 0) {
                recordView(getCurrentReelId());
            }

            container.addEventListener("contextmenu", e => e.preventDefault());
        });
    </script>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('public.reels.standalone');

// صفحة الريلز المحسنة - تجمع بين التصفح والعرض التلقائي
Route::get('reels-enhanced', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $featuredReels = \Botble\VendorReels\Models\VendorReel::published()
            ->featured()
            ->with('store')
            ->limit(6)
            ->get();

        $popularReels = \Botble\VendorReels\Models\VendorReel::published()
            ->with('store')
            ->orderBy('views_count', 'desc')
            ->limit(6)
            ->get();

        $stores = \Botble\Marketplace\Models\Store::whereHas('reels', function($query) {
            $query->where('status', 'published');
        })->limit(10)->get();

        return view('plugins/vendor-reels::public.reels-enhanced', compact('reels', 'featuredReels', 'popularReels', 'stores'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('public.reels.enhanced');

// صفحة الريلز المحدثة مع إصلاح الفيديوهات والتفاعلات
Route::get('reels-fixed', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="' . csrf_token() . '">
    <title>الريلز - نمط Instagram (محدث)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { background: #000; overflow: hidden; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .reels-container { position: relative; width: 100vw; height: 100vh; overflow: hidden; }
        .reel-item { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; opacity: 0; transform: translateY(100%); transition: all 0.3s ease; }
        .reel-item.active { opacity: 1; transform: translateY(0); }
        .reel-item.prev { transform: translateY(-100%); }
        .reel-item.next { transform: translateY(100%); }
        .reel-video-container { position: relative; width: 100%; height: 100%; max-width: 400px; background: #000; border-radius: 0; overflow: hidden; }
        .reel-video { width: 100%; height: 100%; object-fit: cover; background: #000; }
        .reel-overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(transparent 60%, rgba(0,0,0,0.8)); pointer-events: none; }
        .reel-content { position: absolute; bottom: 0; left: 0; right: 0; padding: 20px; color: white; z-index: 10; }
        .reel-title { font-size: 16px; font-weight: 600; margin-bottom: 8px; line-height: 1.3; }
        .reel-description { font-size: 14px; opacity: 0.9; margin-bottom: 12px; line-height: 1.4; }
        .reel-store { display: flex; align-items: center; margin-bottom: 15px; }
        .store-avatar { width: 32px; height: 32px; border-radius: 50%; background: #333; margin-left: 10px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: bold; }
        .store-name { font-size: 14px; font-weight: 500; }
        .reel-actions { position: absolute; right: 15px; bottom: 80px; display: flex; flex-direction: column; gap: 20px; z-index: 10; }
        .action-btn { width: 48px; height: 48px; border-radius: 50%; background: rgba(255,255,255,0.2); border: none; color: white; display: flex; align-items: center; justify-content: center; font-size: 20px; cursor: pointer; transition: all 0.2s ease; backdrop-filter: blur(10px); }
        .action-btn:hover { background: rgba(255,255,255,0.3); transform: scale(1.1); }
        .action-btn.liked { background: #ff3040; }
        .action-count { font-size: 12px; text-align: center; margin-top: 4px; font-weight: 500; }
        .reel-progress { position: absolute; top: 10px; left: 10px; right: 10px; height: 2px; background: rgba(255,255,255,0.3); border-radius: 1px; z-index: 10; }
        .progress-bar { height: 100%; background: white; border-radius: 1px; width: 0%; transition: width 0.1s linear; }
        .navigation-hint { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: rgba(255,255,255,0.7); font-size: 14px; text-align: center; z-index: 5; pointer-events: none; opacity: 0; transition: opacity 0.3s ease; }
        .reels-container:hover .navigation-hint { opacity: 1; }
        .close-btn { position: absolute; top: 20px; right: 20px; width: 40px; height: 40px; border-radius: 50%; background: rgba(0,0,0,0.5); border: none; color: white; display: flex; align-items: center; justify-content: center; font-size: 18px; cursor: pointer; z-index: 20; backdrop-filter: blur(10px); }
        .reel-placeholder { width: 100%; height: 100%; background: linear-gradient(45deg, #333, #555); display: flex; align-items: center; justify-content: center; color: white; font-size: 48px; }
        .success-message { position: fixed; top: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.8); color: white; padding: 10px 20px; border-radius: 20px; font-size: 14px; z-index: 1000; opacity: 0; transition: opacity 0.3s ease; }
        .success-message.show { opacity: 1; }
        .video-loading { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 14px; }
        @media (max-width: 768px) {
            .reel-video-container { max-width: 100%; border-radius: 0; }
            .reel-content { padding: 15px; }
            .reel-actions { right: 10px; bottom: 60px; gap: 15px; }
            .action-btn { width: 44px; height: 44px; font-size: 18px; }
        }
    </style>
</head>
<body>
    <div class="reels-container" id="reelsContainer">';

        if ($reels->count() > 0) {
            foreach ($reels as $index => $reel) {
                $activeClass = $index === 0 ? 'active' : '';
                $html .= '
                <div class="reel-item ' . $activeClass . '" data-reel-id="' . $reel->id . '" data-index="' . $index . '">
                    <div class="reel-video-container">';

                if ($reel->video_url) {
                    $html .= '
                        <video
                            class="reel-video"
                            src="' . $reel->video_url . '"
                            loop
                            muted
                            playsinline
                            preload="metadata"
                            crossorigin="anonymous"
                            data-reel-video="' . $reel->id . '"
                        ></video>
                        <div class="video-loading" style="display: none;">جاري التحميل...</div>';
                } else {
                    $html .= '
                        <div class="reel-placeholder">
                            <span>▶</span>
                        </div>';
                }

                $html .= '
                        <div class="reel-overlay"></div>

                        <div class="reel-progress">
                            <div class="progress-bar" data-progress="' . $reel->id . '"></div>
                        </div>

                        <div class="reel-content">
                            <div class="reel-store">
                                <div class="store-avatar">
                                    ' . substr($reel->store->name ?? 'S', 0, 1) . '
                                </div>
                                <div class="store-name">' . ($reel->store->name ?? 'متجر غير معروف') . '</div>
                            </div>

                            <h3 class="reel-title">' . $reel->title . '</h3>';

                if ($reel->description) {
                    $html .= '<p class="reel-description">' . \Illuminate\Support\Str::limit($reel->description, 100) . '</p>';
                }

                $html .= '
                        </div>

                        <div class="reel-actions">
                            <div>
                                <button class="action-btn like-btn" data-reel-id="' . $reel->id . '">
                                    ♥
                                </button>
                                <div class="action-count">' . format_views_count($reel->likes_count) . '</div>
                            </div>

                            <div>
                                <button class="action-btn comment-btn" data-reel-id="' . $reel->id . '">
                                    💬
                                </button>
                                <div class="action-count">' . format_views_count($reel->comments_count) . '</div>
                            </div>

                            <div>
                                <button class="action-btn share-btn" data-reel-id="' . $reel->id . '">
                                    📤
                                </button>
                                <div class="action-count">' . format_views_count($reel->shares_count) . '</div>
                            </div>

                            <div>
                                <button class="action-btn save-btn" data-reel-id="' . $reel->id . '">
                                    🔖
                                </button>
                            </div>
                        </div>
                    </div>
                </div>';
            }
        } else {
            $html .= '
            <div class="reel-item active">
                <div class="reel-video-container">
                    <div class="reel-placeholder">
                        <div style="text-align: center; color: white;">
                            <div style="font-size: 64px; margin-bottom: 20px;">📹</div>
                            <h3>لا توجد ريلز متاحة</h3>
                            <p>تحقق مرة أخرى لاحقاً</p>
                        </div>
                    </div>
                </div>
            </div>';
        }

        $html .= '
        <button class="close-btn" onclick="window.history.back()">
            ✕
        </button>

        <div class="navigation-hint">
            <div>اسحب لأعلى أو لأسفل للتنقل</div>
            <div style="margin-top: 5px; font-size: 12px;">أو استخدم الأسهم ↑ ↓</div>
        </div>

        <div class="success-message" id="successMessage"></div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const container = document.getElementById("reelsContainer");
            const reelItems = document.querySelectorAll(".reel-item");
            const successMessage = document.getElementById("successMessage");
            let currentIndex = 0;
            let isTransitioning = false;

            if (reelItems.length > 0) {
                playCurrentVideo();
            }

            function showMessage(text) {
                successMessage.textContent = text;
                successMessage.classList.add("show");
                setTimeout(() => {
                    successMessage.classList.remove("show");
                }, 2000);
            }

            function goToNext() {
                if (isTransitioning || currentIndex >= reelItems.length - 1) return;

                isTransitioning = true;

                reelItems[currentIndex].classList.remove("active");
                reelItems[currentIndex].classList.add("prev");

                currentIndex++;

                reelItems[currentIndex].classList.remove("next");
                reelItems[currentIndex].classList.add("active");

                playCurrentVideo();
                recordView(getCurrentReelId());

                setTimeout(() => {
                    isTransitioning = false;
                }, 300);
            }

            function goToPrev() {
                if (isTransitioning || currentIndex <= 0) return;

                isTransitioning = true;

                reelItems[currentIndex].classList.remove("active");
                reelItems[currentIndex].classList.add("next");

                currentIndex--;

                reelItems[currentIndex].classList.remove("prev");
                reelItems[currentIndex].classList.add("active");

                playCurrentVideo();
                recordView(getCurrentReelId());

                setTimeout(() => {
                    isTransitioning = false;
                }, 300);
            }

            function playCurrentVideo() {
                document.querySelectorAll(".reel-video").forEach(video => {
                    video.pause();
                    video.currentTime = 0;
                });

                const currentVideo = reelItems[currentIndex].querySelector(".reel-video");
                const loadingIndicator = reelItems[currentIndex].querySelector(".video-loading");

                if (currentVideo) {
                    if (loadingIndicator) loadingIndicator.style.display = "block";

                    currentVideo.addEventListener("loadeddata", function() {
                        if (loadingIndicator) loadingIndicator.style.display = "none";
                    });

                    currentVideo.addEventListener("error", function() {
                        if (loadingIndicator) {
                            loadingIndicator.textContent = "خطأ في تحميل الفيديو";
                            loadingIndicator.style.color = "#ff6b6b";
                        }
                        console.error("Video error:", currentVideo.src);
                    });

                    currentVideo.play().catch(e => {
                        console.log("Video play failed:", e);
                        if (loadingIndicator) {
                            loadingIndicator.textContent = "فشل تشغيل الفيديو";
                            loadingIndicator.style.color = "#ff6b6b";
                        }
                    });

                    updateProgress(currentVideo);
                }
            }

            function updateProgress(video) {
                const progressBar = video.parentElement.querySelector(".progress-bar");
                if (!progressBar) return;

                const updateProgressBar = () => {
                    if (video.duration) {
                        const progress = (video.currentTime / video.duration) * 100;
                        progressBar.style.width = progress + "%";
                    }
                };

                video.addEventListener("timeupdate", updateProgressBar);
                video.addEventListener("ended", () => {
                    progressBar.style.width = "100%";
                    setTimeout(goToNext, 500);
                });
            }

            function getCurrentReelId() {
                return reelItems[currentIndex].dataset.reelId;
            }

            // Touch/Swipe handling
            let startY = 0;
            let startTime = 0;

            container.addEventListener("touchstart", (e) => {
                startY = e.touches[0].clientY;
                startTime = Date.now();
            });

            container.addEventListener("touchend", (e) => {
                const endY = e.changedTouches[0].clientY;
                const endTime = Date.now();
                const deltaY = startY - endY;
                const deltaTime = endTime - startTime;

                if (Math.abs(deltaY) > 50 && deltaTime < 300) {
                    if (deltaY > 0) {
                        goToNext();
                    } else {
                        goToPrev();
                    }
                }
            });

            // Keyboard navigation
            document.addEventListener("keydown", (e) => {
                switch(e.key) {
                    case "ArrowUp":
                        e.preventDefault();
                        goToPrev();
                        break;
                    case "ArrowDown":
                        e.preventDefault();
                        goToNext();
                        break;
                    case "Escape":
                        window.history.back();
                        break;
                }
            });

            // Mouse wheel navigation
            container.addEventListener("wheel", (e) => {
                e.preventDefault();
                if (e.deltaY > 0) {
                    goToNext();
                } else {
                    goToPrev();
                }
            });

            // Action buttons
            document.querySelectorAll(".like-btn").forEach(btn => {
                btn.addEventListener("click", function() {
                    const reelId = this.dataset.reelId;
                    toggleLike(reelId, this);
                });
            });

            function recordView(reelId) {
                fetch(`/ajax-reel-view/${reelId}`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    }
                }).catch(e => console.log("View recording failed:", e));
            }

            function toggleLike(reelId, button) {
                const isLiked = button.classList.contains("liked");

                if (isLiked) {
                    // إلغاء الإعجاب (للاختبار فقط)
                    button.classList.remove("liked");
                    showMessage("تم إلغاء الإعجاب");
                    return;
                }

                fetch(`/ajax-reel-like/${reelId}`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        button.classList.add("liked");
                        const countElement = button.parentElement.querySelector(".action-count");
                        if (countElement && data.likes_count !== undefined) {
                            countElement.textContent = formatCount(data.likes_count);
                        }
                        showMessage("تم الإعجاب!");
                    } else {
                        showMessage(data.error || "حدث خطأ");
                    }
                })
                .catch(e => {
                    console.log("Like toggle failed:", e);
                    showMessage("حدث خطأ في الاتصال");
                });
            }

            function formatCount(count) {
                if (count >= 1000000) {
                    return (count / 1000000).toFixed(1) + "M";
                } else if (count >= 1000) {
                    return (count / 1000).toFixed(1) + "K";
                }
                return count.toString();
            }

            if (reelItems.length > 0) {
                recordView(getCurrentReelId());
            }

            container.addEventListener("contextmenu", e => e.preventDefault());
        });
    </script>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('public.reels.fixed');

// صفحة مقارنة أنماط العرض
Route::get('reels-styles-demo', function() {
    try {
        $reels = \Botble\VendorReels\Models\VendorReel::published()
            ->with(['store'])
            ->limit(4)
            ->get();

        $html = '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقارنة أنماط عرض الريلز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .demo-card { transition: transform 0.3s ease, box-shadow 0.3s ease; border-radius: 15px; overflow: hidden; }
        .demo-card:hover { transform: translateY(-10px); box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .style-preview { height: 300px; background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); position: relative; overflow: hidden; }
        .instagram-preview { background: linear-gradient(45deg, #833ab4 0%, #fd1d1d 50%, #fcb045 100%); }
        .grid-preview { background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); }
        .preview-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; }
        .feature-list { list-style: none; padding: 0; }
        .feature-list li { padding: 8px 0; border-bottom: 1px solid #eee; }
        .feature-list li:last-child { border-bottom: none; }
        .feature-icon { color: #28a745; margin-left: 10px; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold mb-3">🎬 أنماط عرض الريلز</h1>
            <p class="lead text-muted">اختر النمط المناسب لموقعك</p>
        </div>

        <div class="row g-4 mb-5">
            <!-- النمط التقليدي -->
            <div class="col-lg-6">
                <div class="card demo-card h-100 border-0 shadow">
                    <div class="style-preview grid-preview">
                        <div class="preview-content">
                            <i class="fas fa-th-large fa-4x mb-3"></i>
                            <h3>النمط التقليدي</h3>
                            <p>عرض شبكي مع فلاتر وبحث</p>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <h4 class="card-title mb-3">العرض التقليدي</h4>
                        <p class="card-text text-muted mb-3">مناسب للتصفح والبحث المتقدم</p>

                        <h6 class="fw-bold mb-2">المميزات:</h6>
                        <ul class="feature-list">
                            <li><i class="fas fa-check feature-icon"></i>عرض شبكي منظم</li>
                            <li><i class="fas fa-check feature-icon"></i>فلاتر البحث المتقدمة</li>
                            <li><i class="fas fa-check feature-icon"></i>معلومات تفصيلية</li>
                            <li><i class="fas fa-check feature-icon"></i>سهولة التنقل</li>
                            <li><i class="fas fa-check feature-icon"></i>متوافق مع جميع الأجهزة</li>
                        </ul>

                        <div class="mt-4">
                            <a href="/reels" class="btn btn-primary me-2">
                                <i class="fas fa-eye me-1"></i>عرض مباشر
                            </a>
                            <a href="/test-reels-html" class="btn btn-outline-primary">
                                <i class="fas fa-code me-1"></i>عرض تجريبي
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نمط Instagram -->
            <div class="col-lg-6">
                <div class="card demo-card h-100 border-0 shadow">
                    <div class="style-preview instagram-preview">
                        <div class="preview-content">
                            <i class="fab fa-instagram fa-4x mb-3"></i>
                            <h3>نمط Instagram</h3>
                            <p>عرض عمودي بملء الشاشة</p>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <h4 class="card-title mb-3">نمط Instagram Reels</h4>
                        <p class="card-text text-muted mb-3">تجربة غامرة مثل Instagram و TikTok</p>

                        <h6 class="fw-bold mb-2">المميزات:</h6>
                        <ul class="feature-list">
                            <li><i class="fas fa-check feature-icon"></i>عرض بملء الشاشة</li>
                            <li><i class="fas fa-check feature-icon"></i>تمرير عمودي سلس</li>
                            <li><i class="fas fa-check feature-icon"></i>تشغيل تلقائي للفيديو</li>
                            <li><i class="fas fa-check feature-icon"></i>تفاعل مباشر (إعجاب، تعليق)</li>
                            <li><i class="fas fa-check feature-icon"></i>تجربة مشابهة للشبكات الاجتماعية</li>
                        </ul>

                        <div class="mt-4">
                            <a href="/reels-instagram" class="btn btn-warning me-2">
                                <i class="fab fa-instagram me-1"></i>عرض مباشر
                            </a>
                            <a href="#" class="btn btn-outline-warning" onclick="showInstructions()">
                                <i class="fas fa-question-circle me-1"></i>كيفية الاستخدام
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الريلز -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-0 shadow">
                    <div class="card-body p-4">
                        <h5 class="card-title mb-4">📊 إحصائيات الريلز الحالية</h5>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-primary">' . $reels->count() . '</h3>
                                    <p class="text-muted mb-0">ريلز متاحة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-success">' . $reels->where('is_featured', true)->count() . '</h3>
                                    <p class="text-muted mb-0">ريلز مميزة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-warning">' . $reels->sum('views_count') . '</h3>
                                    <p class="text-muted mb-0">إجمالي المشاهدات</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-info">' . $reels->sum('likes_count') . '</h3>
                                    <p class="text-muted mb-0">إجمالي الإعجابات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط إضافية -->
        <div class="text-center">
            <h5 class="mb-3">روابط مفيدة</h5>
            <a href="/test-reels-dashboard" class="btn btn-outline-primary me-2">
                <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
            </a>
            <a href="/vendor/reels" class="btn btn-outline-success me-2">
                <i class="fas fa-plus me-1"></i>إضافة ريل جديد
            </a>
            <a href="/v1/reels" class="btn btn-outline-info" target="_blank">
                <i class="fas fa-code me-1"></i>API الريلز
            </a>
        </div>
    </div>

    <!-- Modal للتعليمات -->
    <div class="modal fade" id="instructionsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">كيفية استخدام نمط Instagram</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>التنقل:</h6>
                    <ul>
                        <li><strong>الهاتف:</strong> اسحب لأعلى أو لأسفل للتنقل بين الريلز</li>
                        <li><strong>الكمبيوتر:</strong> استخدم الأسهم ↑ ↓ أو عجلة الماوس</li>
                        <li><strong>الخروج:</strong> اضغط ESC أو زر الإغلاق ✕</li>
                    </ul>

                    <h6>التفاعل:</h6>
                    <ul>
                        <li>اضغط على ❤️ للإعجاب</li>
                        <li>اضغط على 💬 للتعليق</li>
                        <li>اضغط على 📤 للمشاركة</li>
                        <li>اضغط على 🔖 للحفظ</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">فهمت</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showInstructions() {
            new bootstrap.Modal(document.getElementById("instructionsModal")).show();
        }
    </script>
</body>
</html>';

        return response($html);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('reels.styles.demo');

// مسارات AJAX بسيطة للتفاعلات
Route::post('ajax-reel-view/{id}', function($id) {
    try {
        $reel = \Botble\VendorReels\Models\VendorReel::findOrFail($id);
        $reel->increment('views_count');

        return response()->json([
            'success' => true,
            'views_count' => $reel->fresh()->views_count
        ]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
    }
})->name('ajax.reel.view');

Route::post('ajax-reel-like/{id}', function($id) {
    try {
        $reel = \Botble\VendorReels\Models\VendorReel::findOrFail($id);
        $reel->increment('likes_count');

        return response()->json([
            'success' => true,
            'likes_count' => $reel->fresh()->likes_count
        ]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
    }
})->name('ajax.reel.like');

// مسارات بديلة بدون CSRF للاختبار
Route::group(['prefix' => 'api/reels', 'middleware' => ['api']], function () {
    Route::post('{reel}/view', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $reel->increment('views_count');

            \Botble\VendorReels\Models\ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => null,
                'type' => 'view',
                'ip_address' => request()->ip()
            ]);

            return response()->json([
                'success' => true,
                'views_count' => $reel->fresh()->views_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.view');

    Route::post('{reel}/like', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $ip = request()->ip();
            $sessionId = session()->getId() ?: 'anonymous';

            $existingLike = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'like',
                'ip_address' => $ip
            ])->where('content', $sessionId)->first();

            if ($existingLike) {
                return response()->json(['success' => false, 'error' => 'تم الإعجاب مسبقاً'], 400);
            }

            \Botble\VendorReels\Models\ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => null,
                'type' => 'like',
                'ip_address' => $ip,
                'content' => $sessionId
            ]);

            $reel->increment('likes_count');

            return response()->json([
                'success' => true,
                'likes_count' => $reel->fresh()->likes_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.like');

    Route::delete('{reel}/unlike', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $ip = request()->ip();
            $sessionId = session()->getId() ?: 'anonymous';

            $interaction = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'like',
                'ip_address' => $ip
            ])->where('content', $sessionId)->first();

            if (!$interaction) {
                return response()->json(['success' => false, 'error' => 'لم يتم الإعجاب مسبقاً'], 400);
            }

            $interaction->delete();
            $reel->decrement('likes_count');

            return response()->json([
                'success' => true,
                'likes_count' => $reel->fresh()->likes_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.unlike');

    Route::get('{reel}/comments', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $comments = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'comment'
            ])
            ->with('customer')
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function($interaction) {
                return [
                    'id' => $interaction->id,
                    'content' => $interaction->content,
                    'customer_name' => $interaction->customer ? $interaction->customer->name : 'زائر',
                    'created_at' => $interaction->created_at->diffForHumans()
                ];
            });

            return response()->json([
                'success' => true,
                'comments' => $comments
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.comments');

    Route::post('{reel}/save', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $ip = request()->ip();
            $sessionId = session()->getId() ?: 'anonymous';

            $existingSave = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'save',
                'ip_address' => $ip
            ])->where('content', $sessionId)->first();

            if ($existingSave) {
                return response()->json(['success' => false, 'error' => 'تم حفظ الريل مسبقاً'], 400);
            }

            \Botble\VendorReels\Models\ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => null,
                'type' => 'save',
                'ip_address' => $ip,
                'content' => $sessionId
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حفظ الريل بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.save');

    Route::delete('{reel}/unsave', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $ip = request()->ip();
            $sessionId = session()->getId() ?: 'anonymous';

            $interaction = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'save',
                'ip_address' => $ip
            ])->where('content', $sessionId)->first();

            if (!$interaction) {
                return response()->json(['success' => false, 'error' => 'لم يتم حفظ الريل مسبقاً'], 400);
            }

            $interaction->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء حفظ الريل'
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.unsave');

    Route::post('{reel}/comment', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $content = request()->input('content');

            if (!$content || trim($content) === '') {
                return response()->json(['success' => false, 'error' => 'يرجى كتابة تعليق'], 400);
            }

            $ip = request()->ip();
            $sessionId = session()->getId() ?: 'anonymous';

            \Botble\VendorReels\Models\ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => null,
                'type' => 'comment',
                'content' => trim($content),
                'ip_address' => $ip
            ]);

            $reel->increment('comments_count');

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة التعليق بنجاح',
                'comments_count' => $reel->fresh()->comments_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('api.reels.comment');
});

// مسارات AJAX للتفاعلات (بدون CSRF للاختبار)
Route::group(['prefix' => 'ajax/reels', 'middleware' => ['web']], function () {
    Route::post('{reel}/view', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            // تسجيل مشاهدة
            $reel->increment('views_count');

            // إضافة تفاعل بدون مصادقة (للاختبار)
            \Botble\VendorReels\Models\ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => null, // زائر غير مسجل
                'type' => 'view',
                'ip_address' => request()->ip()
            ]);

            return response()->json([
                'success' => true,
                'views_count' => $reel->fresh()->views_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('ajax.reels.view');

    Route::post('{reel}/like', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            // للاختبار: السماح بالإعجاب بدون مصادقة
            $ip = request()->ip();
            $sessionId = session()->getId();

            // التحقق من الإعجاب المسبق بناءً على IP + Session
            $existingLike = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'like',
                'ip_address' => $ip
            ])->where('content', $sessionId)->first();

            if ($existingLike) {
                return response()->json(['success' => false, 'error' => 'تم الإعجاب مسبقاً'], 400);
            }

            \Botble\VendorReels\Models\ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => null,
                'type' => 'like',
                'ip_address' => $ip,
                'content' => $sessionId // استخدام session ID للتمييز
            ]);

            $reel->increment('likes_count');

            return response()->json([
                'success' => true,
                'likes_count' => $reel->fresh()->likes_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('ajax.reels.like');

    Route::delete('{reel}/unlike', function(\Botble\VendorReels\Models\VendorReel $reel) {
        try {
            $ip = request()->ip();
            $sessionId = session()->getId();

            $interaction = \Botble\VendorReels\Models\ReelInteraction::where([
                'reel_id' => $reel->id,
                'type' => 'like',
                'ip_address' => $ip
            ])->where('content', $sessionId)->first();

            if (!$interaction) {
                return response()->json(['success' => false, 'error' => 'لم يتم الإعجاب مسبقاً'], 400);
            }

            $interaction->delete();
            $reel->decrement('likes_count');

            return response()->json([
                'success' => true,
                'likes_count' => $reel->fresh()->likes_count
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    })->name('ajax.reels.unlike');
});

// مسارات عامة للريلز (للعملاء)
Route::group([
    'prefix' => 'reels',
    'as' => 'public.reels.',
    'middleware' => ['web'],
], function () {
    
    // صفحة تصفح الريلز
    Route::get('/', [PublicReelController::class, 'index'])->name('index');
    
    // عرض ريل محدد
    Route::get('/{reel}', [PublicReelController::class, 'show'])->name('show');
    
    // ريلز متجر محدد
    Route::get('/store/{store}', [PublicReelController::class, 'storeReels'])->name('store');
    
    // ريلز حسب الفئة
    Route::get('/category/{category}', [PublicReelController::class, 'categoryReels'])->name('category');
    
    // البحث في الريلز
    Route::get('/search', [PublicReelController::class, 'search'])->name('search');
});

// مسارات التفاعل مع الريلز (تتطلب تسجيل دخول)
Route::group([
    'prefix' => 'reels',
    'as' => 'public.reels.',
    'middleware' => ['web', 'customer'],
], function () {
    
    // التفاعل مع الريل
    Route::post('/{reel}/like', [ReelInteractionController::class, 'like'])->name('like');
    Route::delete('/{reel}/unlike', [ReelInteractionController::class, 'unlike'])->name('unlike');
    Route::post('/{reel}/comment', [ReelInteractionController::class, 'comment'])->name('comment');
    Route::post('/{reel}/share', [ReelInteractionController::class, 'share'])->name('share');
    Route::post('/{reel}/save', [ReelInteractionController::class, 'save'])->name('save');
    Route::delete('/{reel}/unsave', [ReelInteractionController::class, 'unsave'])->name('unsave');
    
    // الريلز المحفوظة للعميل
    Route::get('/saved', [ReelInteractionController::class, 'savedReels'])->name('saved');
});

// مسارات AJAX للتفاعل
Route::group([
    'prefix' => 'ajax/reels',
    'as' => 'ajax.reels.',
    'middleware' => ['web'],
], function () {
    
    // تسجيل مشاهدة
    Route::post('/{reel}/view', [ReelInteractionController::class, 'recordView'])->name('view');
    
    // الحصول على التعليقات
    Route::get('/{reel}/comments', [ReelInteractionController::class, 'getComments'])->name('comments');
    
    // الحصول على الريلز التالية (infinite scroll)
    Route::get('/load-more', [PublicReelController::class, 'loadMore'])->name('load-more');
    
    // الحصول على ريلز مشابهة
    Route::get('/{reel}/related', [PublicReelController::class, 'getRelated'])->name('related');
});

/*
|--------------------------------------------------------------------------
| Messenger & Social Features Routes
|--------------------------------------------------------------------------
|
| Routes for the messenger and social features
|
*/

Route::group([
    'prefix' => 'messenger',
    'as' => 'messenger.',
    'middleware' => ['web', 'auth:customer'],
], function () {

    // الماسنجر
    Route::get('/', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'index'])->name('index');

    // القصص
    Route::get('/stories', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'stories'])->name('stories');

    // الريلز
    Route::get('/reels', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'reels'])->name('reels');

    // الصفحة الرئيسية (الفيد)
    Route::get('/feed', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'feed'])->name('feed');

    // الإشعارات
    Route::get('/notifications', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'notifications'])->name('notifications');

    // البحث
    Route::get('/search', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'search'])->name('search');

    // الاستكشاف
    Route::get('/explore', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'explore'])->name('explore');

    // الملف الشخصي
    Route::get('/profile/{userId}', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'profile'])->name('profile');
});

// Routes عامة (بدون تسجيل دخول)
Route::group([
    'prefix' => 'social',
    'as' => 'social.',
    'middleware' => ['web'],
], function () {

    // عرض الريلز العامة
    Route::get('/reels', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'reels'])->name('reels.public');

    // عرض القصص العامة
    Route::get('/stories', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'stories'])->name('stories.public');

    // الاستكشاف العام
    Route::get('/explore', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'explore'])->name('explore.public');

    // البحث العام
    Route::get('/search', [\Botble\VendorReels\Http\Controllers\MessengerController::class, 'search'])->name('search.public');
});
