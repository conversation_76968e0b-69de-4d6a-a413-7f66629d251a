<?php

namespace Bo<PERSON>ble\VendorReels\Commands;

use Botble\VendorReels\Models\UserStory;
use Illuminate\Console\Command;
use Botble\Media\Facades\RvMedia;

class CleanupExpiredStoriesCommand extends Command
{
    protected $signature = 'stories:cleanup-expired';
    protected $description = 'Clean up expired stories and their media files';

    public function handle(): int
    {
        $this->info('Starting cleanup of expired stories...');

        // جلب القصص المنتهية الصلاحية
        $expiredStories = UserStory::where('expires_at', '<', now())
            ->orWhere('is_active', false)
            ->get();

        $deletedCount = 0;
        $deletedFilesCount = 0;

        foreach ($expiredStories as $story) {
            try {
                // حذف الملف المرفق إذا وجد
                if ($story->media_url && in_array($story->media_type, ['image', 'video'])) {
                    try {
                        RvMedia::deleteFile($story->media_url);
                        $deletedFilesCount++;
                        $this->line("Deleted media file: {$story->media_url}");
                    } catch (\Exception $e) {
                        $this->warn("Failed to delete media file {$story->media_url}: {$e->getMessage()}");
                    }
                }

                // حذف القصة
                $story->delete();
                $deletedCount++;

                $this->line("Deleted story ID: {$story->id}");

            } catch (\Exception $e) {
                $this->error("Failed to delete story ID {$story->id}: {$e->getMessage()}");
            }
        }

        $this->info("Cleanup completed!");
        $this->info("Deleted {$deletedCount} expired stories");
        $this->info("Deleted {$deletedFilesCount} media files");

        return self::SUCCESS;
    }
}
