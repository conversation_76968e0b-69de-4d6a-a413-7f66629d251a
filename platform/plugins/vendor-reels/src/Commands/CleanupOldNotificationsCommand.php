<?php

namespace Botble\VendorReels\Commands;

use Botble\VendorReels\Models\UserNotification;
use Illuminate\Console\Command;

class CleanupOldNotificationsCommand extends Command
{
    protected $signature = 'notifications:cleanup-old {--days=30 : Number of days to keep notifications}';
    protected $description = 'Clean up old notifications from the database';

    public function handle(): int
    {
        $days = (int) $this->option('days');
        
        $this->info("Starting cleanup of notifications older than {$days} days...");

        // حذف الإشعارات القديمة
        $deletedCount = UserNotification::where('created_at', '<', now()->subDays($days))
            ->delete();

        $this->info("Cleanup completed!");
        $this->info("Deleted {$deletedCount} old notifications");

        return self::SUCCESS;
    }
}
