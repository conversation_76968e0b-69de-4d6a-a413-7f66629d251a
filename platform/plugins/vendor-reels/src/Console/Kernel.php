<?php

namespace Botble\VendorReels\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // تنظيف القصص المنتهية الصلاحية كل ساعة
        $schedule->command('stories:cleanup-expired')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();

        // تنظيف الإشعارات القديمة يومياً في الساعة 2:00 صباحاً
        $schedule->command('notifications:cleanup-old --days=30')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // تنظيف الريلز القديمة أسبوعياً
        $schedule->command('reels:cleanup-old')
                 ->weekly()
                 ->sundays()
                 ->at('03:00')
                 ->withoutOverlapping()
                 ->runInBackground();
    }
}
