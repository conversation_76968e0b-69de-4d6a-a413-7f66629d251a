<?php

namespace Bo<PERSON>ble\VendorReels\Events;

use Botble\VendorReels\Models\UserStory;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StoryCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public UserStory $story)
    {
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [
            new Channel('stories'),
        ];

        // إرسال للمتابعين إذا كانت القصة للمتابعين أو عامة
        if (in_array($this->story->privacy, ['public', 'followers'])) {
            $followers = $this->story->user->followers;
            foreach ($followers as $follower) {
                $channels[] = new PrivateChannel('user.' . $follower->id);
            }
        }

        return $channels;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'story.created';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'story' => [
                'id' => $this->story->id,
                'user_id' => $this->story->user_id,
                'media_type' => $this->story->media_type,
                'created_at' => $this->story->created_at->toISOString(),
                'user' => [
                    'id' => $this->story->user->id,
                    'name' => $this->story->user->name,
                    'avatar' => $this->story->user->avatar_url,
                ],
            ],
        ];
    }
}
