<?php

namespace Bo<PERSON>ble\VendorReels\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON><PERSON>\VendorReels\Models\Conversation;
use Bo<PERSON><PERSON>\VendorReels\Models\Message;
use Botble\VendorReels\Http\Resources\ConversationResource;
use Botble\VendorReels\Http\Resources\MessageResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ConversationApiController extends BaseController
{
    /**
     * Get user conversations
     */
    public function index(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $conversations = Conversation::forCustomer($customer->id)
            ->with(['lastMessage.sender', 'participants', 'store'])
            ->orderBy('last_message_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب المحادثات بنجاح',
            'data' => ConversationResource::collection($conversations),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get specific conversation
     */
    public function show(Conversation $conversation): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if (!$conversation->isParticipant($customer->id)) {
            return response()->json([
                'success' => false,
                'message' => 'غير مسموح لك بالوصول لهذه المحادثة',
                'error' => 'Access denied',
            ], 403);
        }

        $conversation->load(['participants', 'store']);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب المحادثة بنجاح',
            'data' => new ConversationResource($conversation),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Create new conversation
     */
    public function store(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'type' => 'required|in:private,group,store_customer',
            'participant_ids' => 'required|array|min:1',
            'participant_ids.*' => 'exists:ec_customers,id',
            'title' => 'nullable|string|max:255',
            'store_id' => 'nullable|exists:mp_stores,id',
            'initial_message' => 'nullable|string|max:1000'
        ]);

        DB::beginTransaction();
        try {
            // إنشاء المحادثة
            $conversation = Conversation::create([
                'type' => $request->type,
                'title' => $request->title,
                'is_group' => $request->type === 'group',
                'created_by' => $customer->id,
                'store_id' => $request->store_id,
            ]);

            // إضافة المنشئ كمشارك
            $conversation->addParticipant($customer->id, true);

            // إضافة المشاركين الآخرين
            foreach ($request->participant_ids as $participantId) {
                if ($participantId != $customer->id) {
                    $conversation->addParticipant($participantId);
                }
            }

            // إرسال رسالة أولية إذا تم توفيرها
            if ($request->initial_message) {
                $message = Message::create([
                    'conversation_id' => $conversation->id,
                    'sender_id' => $customer->id,
                    'content' => $request->initial_message,
                    'type' => Message::TYPE_TEXT,
                ]);

                $conversation->updateLastMessage($message);
            }

            DB::commit();

            $conversation->load(['participants', 'store', 'lastMessage']);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المحادثة بنجاح',
                'data' => new ConversationResource($conversation),
                'timestamp' => now()->toISOString(),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء المحادثة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Start conversation with store
     */
    public function startWithStore(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'store_id' => 'required|exists:mp_stores,id',
            'initial_message' => 'nullable|string|max:1000'
        ]);

        $storeId = $request->store_id;

        // البحث عن محادثة موجودة مع المتجر
        $existingConversation = Conversation::where('type', Conversation::TYPE_STORE_CUSTOMER)
            ->where('store_id', $storeId)
            ->whereHas('participants', function ($query) use ($customer) {
                $query->where('customer_id', $customer->id)
                      ->whereNull('left_at');
            })
            ->first();

        if ($existingConversation) {
            return response()->json([
                'success' => true,
                'message' => 'المحادثة موجودة مسبقاً',
                'data' => new ConversationResource($existingConversation->load(['participants', 'store', 'lastMessage'])),
                'timestamp' => now()->toISOString(),
            ]);
        }

        // إنشاء محادثة جديدة
        DB::beginTransaction();
        try {
            $store = \Botble\Marketplace\Models\Store::find($storeId);
            
            $conversation = Conversation::create([
                'type' => Conversation::TYPE_STORE_CUSTOMER,
                'title' => "محادثة مع {$store->name}",
                'is_group' => false,
                'created_by' => $customer->id,
                'store_id' => $storeId,
            ]);

            // إضافة العميل
            $conversation->addParticipant($customer->id);
            
            // إضافة صاحب المتجر
            $conversation->addParticipant($store->customer_id, true);

            // إرسال رسالة أولية إذا تم توفيرها
            if ($request->initial_message) {
                $message = Message::create([
                    'conversation_id' => $conversation->id,
                    'sender_id' => $customer->id,
                    'content' => $request->initial_message,
                    'type' => Message::TYPE_TEXT,
                ]);

                $conversation->updateLastMessage($message);
            }

            DB::commit();

            $conversation->load(['participants', 'store', 'lastMessage']);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المحادثة مع المتجر بنجاح',
                'data' => new ConversationResource($conversation),
                'timestamp' => now()->toISOString(),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء المحادثة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark conversation as read
     */
    public function markAsRead(Conversation $conversation): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if (!$conversation->isParticipant($customer->id)) {
            return response()->json([
                'success' => false,
                'message' => 'غير مسموح لك بالوصول لهذه المحادثة',
                'error' => 'Access denied',
            ], 403);
        }

        $conversation->markAsRead($customer->id);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد المحادثة كمقروءة',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Add participant to group conversation
     */
    public function addParticipant(Conversation $conversation, Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if (!$conversation->isParticipant($customer->id) || !$conversation->is_group) {
            return response()->json([
                'success' => false,
                'message' => 'غير مسموح لك بإضافة مشاركين',
                'error' => 'Access denied',
            ], 403);
        }

        $request->validate([
            'customer_id' => 'required|exists:ec_customers,id'
        ]);

        $newParticipantId = $request->customer_id;

        if ($conversation->isParticipant($newParticipantId)) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم مشارك في المحادثة بالفعل',
                'error' => 'Already participant',
            ], 400);
        }

        $conversation->addParticipant($newParticipantId);

        // إرسال رسالة نظام
        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $customer->id,
            'content' => "تم إضافة مشارك جديد للمحادثة",
            'type' => Message::TYPE_SYSTEM,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة المشارك بنجاح',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Leave conversation
     */
    public function leave(Conversation $conversation): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if (!$conversation->isParticipant($customer->id)) {
            return response()->json([
                'success' => false,
                'message' => 'أنت لست مشارك في هذه المحادثة',
                'error' => 'Not participant',
            ], 400);
        }

        $conversation->removeParticipant($customer->id);

        // إرسال رسالة نظام
        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $customer->id,
            'content' => "غادر المحادثة",
            'type' => Message::TYPE_SYSTEM,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم مغادرة المحادثة بنجاح',
            'timestamp' => now()->toISOString(),
        ]);
    }
}
