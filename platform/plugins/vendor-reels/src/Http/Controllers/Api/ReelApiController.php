<?php

namespace Bo<PERSON>ble\VendorReels\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON><PERSON>\VendorReels\Models\VendorReel;
use Bo<PERSON><PERSON>\VendorReels\Models\ReelInteraction;
use Bo<PERSON>ble\VendorReels\Repositories\Interfaces\VendorReelInterface;
use Botble\VendorReels\Http\Resources\ReelResource;
use Botble\VendorReels\Http\Resources\ReelCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ReelApiController extends BaseController
{
    public function __construct(protected VendorReelInterface $vendorReelRepository)
    {
    }

    /**
     * Get list of published reels
     */
    public function index(Request $request): JsonResponse
    {
        $reels = $this->vendorReelRepository->getPublishedReels([
            'search' => $request->get('search'),
            'store_id' => $request->get('store_id'),
            'per_page' => min($request->get('per_page', 15), 50) // حد أقصى 50
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الريلز بنجاح',
            'data' => new ReelCollection($reels),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get featured reels
     */
    public function featured(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 20);
        $reels = $this->vendorReelRepository->getFeaturedReels($limit);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الريلز المميزة بنجاح',
            'data' => ReelResource::collection($reels),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get popular reels
     */
    public function popular(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 20);
        $reels = $this->vendorReelRepository->getPopularReels($limit);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الريلز الشائعة بنجاح',
            'data' => ReelResource::collection($reels),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get recent reels
     */
    public function recent(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 20);
        $reels = $this->vendorReelRepository->getRecentReels($limit);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الريلز الحديثة بنجاح',
            'data' => ReelResource::collection($reels),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get single reel
     */
    public function show(VendorReel $reel): JsonResponse
    {
        if ($reel->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'الريل غير موجود',
                'error' => 'Reel not found or not published',
            ], 404);
        }

        $reel->load(['store', 'products.categories']);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الريل بنجاح',
            'data' => new ReelResource($reel),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get reels by store
     */
    public function byStore(Request $request, int $storeId): JsonResponse
    {
        $reels = $this->vendorReelRepository->getByStore($storeId, [
            'status' => 'published',
            'search' => $request->get('search'),
            'per_page' => min($request->get('per_page', 15), 50)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب ريلز المتجر بنجاح',
            'data' => new ReelCollection($reels),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Search reels
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q');

        if (!$query) {
            return response()->json([
                'success' => false,
                'message' => 'يرجى إدخال كلمة البحث',
                'error' => 'Search query is required',
            ], 400);
        }

        $reels = VendorReel::published()
            ->where(function($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%");
            })
            ->with(['store'])
            ->orderBy('views_count', 'desc')
            ->paginate(min($request->get('per_page', 15), 50));

        return response()->json([
            'success' => true,
            'message' => 'تم البحث بنجاح',
            'data' => new ReelCollection($reels),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Record view
     */
    public function recordView(VendorReel $reel, Request $request): JsonResponse
    {
        if ($reel->status !== 'published') {
            return $this->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('الريل غير موجود');
        }

        $customer = auth('sanctum')->user();
        $ip = $request->ip();

        // تجنب تسجيل مشاهدات متكررة
        $existingView = ReelInteraction::where([
            'reel_id' => $reel->id,
            'type' => ReelInteraction::TYPE_VIEW
        ])
        ->where(function($query) use ($customer, $ip) {
            if ($customer) {
                $query->where('customer_id', $customer->id);
            } else {
                $query->where('ip_address', $ip);
            }
        })
        ->whereDate('created_at', today())
        ->first();

        if (!$existingView) {
            ReelInteraction::create([
                'reel_id' => $reel->id,
                'customer_id' => $customer?->id,
                'type' => ReelInteraction::TYPE_VIEW,
                'ip_address' => $ip
            ]);

            $reel->incrementViews();
        }

        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل المشاهدة',
            'data' => ['views_count' => $reel->fresh()->views_count],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Like reel
     */
    public function like(VendorReel $reel): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if ($reel->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'الريل غير موجود',
                'error' => 'Reel not found or not published',
            ], 404);
        }

        $interaction = ReelInteraction::where([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_LIKE
        ])->first();

        if ($interaction) {
            return response()->json([
                'success' => false,
                'message' => 'تم الإعجاب مسبقاً',
                'error' => 'Already liked',
            ], 400);
        }

        ReelInteraction::create([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_LIKE,
            'ip_address' => request()->ip()
        ]);

        $reel->incrementLikes();

        return response()->json([
            'success' => true,
            'message' => 'تم الإعجاب بالريل',
            'data' => ['likes_count' => $reel->fresh()->likes_count],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Unlike reel
     */
    public function unlike(VendorReel $reel): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $interaction = ReelInteraction::where([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_LIKE
        ])->first();

        if (!$interaction) {
            return response()->json([
                'success' => false,
                'message' => 'لم يتم الإعجاب مسبقاً',
                'error' => 'Not liked before',
            ], 400);
        }

        $interaction->delete();
        $reel->decrementLikes();

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء الإعجاب بنجاح',
            'data' => ['likes_count' => $reel->fresh()->likes_count],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Add comment
     */
    public function comment(VendorReel $reel, Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if ($reel->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'الريل غير موجود',
                'error' => 'Reel not found or not published',
            ], 404);
        }

        $request->validate([
            'content' => 'required|string|max:500'
        ]);

        $comment = ReelInteraction::create([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_COMMENT,
            'content' => sanitize_reel_content($request->input('content')),
            'ip_address' => $request->ip()
        ]);

        $reel->incrementComments();

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة التعليق بنجاح',
            'data' => [
                'comment' => [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'customer_name' => $customer->name,
                    'customer_avatar' => $customer->avatar_url ?? null,
                    'created_at' => $comment->created_at->diffForHumans()
                ],
                'comments_count' => $reel->fresh()->comments_count
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get reel comments
     */
    public function comments(VendorReel $reel, Request $request): JsonResponse
    {
        if ($reel->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'الريل غير موجود',
                'error' => 'Reel not found or not published',
            ], 404);
        }

        $comments = ReelInteraction::where([
            'reel_id' => $reel->id,
            'type' => ReelInteraction::TYPE_COMMENT
        ])
        ->with('customer')
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        $commentsData = $comments->map(function($comment) {
            return [
                'id' => $comment->id,
                'content' => $comment->content,
                'customer_name' => $comment->customer?->name ?? 'مجهول',
                'customer_avatar' => $comment->customer?->avatar_url ?? null,
                'created_at' => $comment->created_at->diffForHumans()
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب التعليقات بنجاح',
            'data' => [
                'comments' => $commentsData,
                'pagination' => [
                    'current_page' => $comments->currentPage(),
                    'last_page' => $comments->lastPage(),
                    'total' => $comments->total(),
                    'per_page' => $comments->perPage()
                ]
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Save reel to favorites
     */
    public function save(VendorReel $reel): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if ($reel->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'الريل غير موجود',
                'error' => 'Reel not found or not published',
            ], 404);
        }

        $interaction = ReelInteraction::where([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_SAVE
        ])->first();

        if ($interaction) {
            return response()->json([
                'success' => false,
                'message' => 'تم حفظ الريل مسبقاً',
                'error' => 'Already saved',
            ], 400);
        }

        ReelInteraction::create([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_SAVE,
            'ip_address' => request()->ip()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم حفظ الريل بنجاح',
            'data' => ['saved' => true],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Remove reel from favorites
     */
    public function unsave(VendorReel $reel): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $interaction = ReelInteraction::where([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_SAVE
        ])->first();

        if (!$interaction) {
            return response()->json([
                'success' => false,
                'message' => 'لم يتم حفظ الريل مسبقاً',
                'error' => 'Not saved before',
            ], 400);
        }

        $interaction->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء حفظ الريل بنجاح',
            'data' => ['saved' => false],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Share reel
     */
    public function share(VendorReel $reel, Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if ($reel->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'الريل غير موجود',
                'error' => 'Reel not found or not published',
            ], 404);
        }

        // تسجيل المشاركة
        ReelInteraction::create([
            'reel_id' => $reel->id,
            'customer_id' => $customer->id,
            'type' => ReelInteraction::TYPE_SHARE,
            'content' => $request->input('platform', 'general'), // منصة المشاركة
            'ip_address' => $request->ip()
        ]);

        $reel->incrementShares();

        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل المشاركة بنجاح',
            'data' => [
                'shares_count' => $reel->fresh()->shares_count,
                'share_url' => route('api.reels.show', $reel->id)
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }
}
