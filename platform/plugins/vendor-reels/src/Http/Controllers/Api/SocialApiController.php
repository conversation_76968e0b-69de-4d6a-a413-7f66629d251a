<?php

namespace Bo<PERSON>ble\VendorReels\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\VendorReels\Models\UserFollow;
use Bo<PERSON>ble\VendorReels\Models\UserNotification;
use Botble\VendorReels\Http\Resources\UserResource;
use Botble\VendorReels\Http\Resources\NotificationResource;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class SocialApiController extends BaseController
{
    /**
     * Follow a user
     */
    public function follow(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'user_id' => 'required|exists:ec_customers,id'
        ]);

        $targetUserId = $request->user_id;

        // لا يمكن متابعة النفس
        if ($customer->id === $targetUserId) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك متابعة نفسك',
                'error' => 'Cannot follow yourself',
            ], 400);
        }

        // التحقق من وجود متابعة سابقة
        $existingFollow = UserFollow::where([
            'follower_id' => $customer->id,
            'following_id' => $targetUserId
        ])->first();

        if ($existingFollow && $existingFollow->status === UserFollow::STATUS_ACCEPTED) {
            return response()->json([
                'success' => false,
                'message' => 'أنت تتابع هذا المستخدم بالفعل',
                'error' => 'Already following',
            ], 400);
        }

        DB::beginTransaction();
        try {
            // إنشاء أو تحديث المتابعة
            $follow = UserFollow::updateOrCreate(
                [
                    'follower_id' => $customer->id,
                    'following_id' => $targetUserId
                ],
                [
                    'status' => UserFollow::STATUS_ACCEPTED,
                    'followed_at' => now(),
                    'unfollowed_at' => null
                ]
            );

            // إرسال إشعار للمستخدم المتابَع
            UserNotification::create([
                'user_id' => $targetUserId,
                'type' => UserNotification::TYPE_FOLLOW,
                'title' => 'متابع جديد',
                'message' => "{$customer->name} بدأ في متابعتك",
                'notifiable_type' => Customer::class,
                'notifiable_id' => $customer->id,
                'action_url' => "/profile/{$customer->id}",
                'priority' => UserNotification::PRIORITY_NORMAL,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم متابعة المستخدم بنجاح',
                'data' => [
                    'following' => true,
                    'follow_id' => $follow->id
                ],
                'timestamp' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء المتابعة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Unfollow a user
     */
    public function unfollow(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'user_id' => 'required|exists:ec_customers,id'
        ]);

        $targetUserId = $request->user_id;

        $follow = UserFollow::where([
            'follower_id' => $customer->id,
            'following_id' => $targetUserId,
            'status' => UserFollow::STATUS_ACCEPTED
        ])->first();

        if (!$follow) {
            return response()->json([
                'success' => false,
                'message' => 'أنت لا تتابع هذا المستخدم',
                'error' => 'Not following',
            ], 400);
        }

        $follow->unfollow();

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء المتابعة بنجاح',
            'data' => ['following' => false],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get user followers
     */
    public function followers(Request $request, int $userId): JsonResponse
    {
        $customer = auth('sanctum')->user();

        $followers = UserFollow::where('following_id', $userId)
            ->accepted()
            ->with('follower')
            ->orderBy('followed_at', 'desc')
            ->paginate(20);

        $followersData = $followers->map(function ($follow) use ($customer) {
            $follower = $follow->follower;
            return [
                'id' => $follower->id,
                'name' => $follower->name,
                'email' => $follower->email,
                'avatar' => $follower->avatar_url,
                'is_vendor' => $follower->is_vendor,
                'followed_at' => $follow->followed_at->toISOString(),
                'is_following_me' => $customer ? UserFollow::where([
                    'follower_id' => $customer->id,
                    'following_id' => $follower->id,
                    'status' => UserFollow::STATUS_ACCEPTED
                ])->exists() : false,
                'is_followed_by_me' => $customer ? UserFollow::where([
                    'follower_id' => $follower->id,
                    'following_id' => $customer->id,
                    'status' => UserFollow::STATUS_ACCEPTED
                ])->exists() : false,
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب المتابعين بنجاح',
            'data' => [
                'followers' => $followersData,
                'pagination' => [
                    'current_page' => $followers->currentPage(),
                    'last_page' => $followers->lastPage(),
                    'total' => $followers->total(),
                    'per_page' => $followers->perPage()
                ]
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get user following
     */
    public function following(Request $request, int $userId): JsonResponse
    {
        $customer = auth('sanctum')->user();

        $following = UserFollow::where('follower_id', $userId)
            ->accepted()
            ->with('following')
            ->orderBy('followed_at', 'desc')
            ->paginate(20);

        $followingData = $following->map(function ($follow) use ($customer) {
            $followingUser = $follow->following;
            return [
                'id' => $followingUser->id,
                'name' => $followingUser->name,
                'email' => $followingUser->email,
                'avatar' => $followingUser->avatar_url,
                'is_vendor' => $followingUser->is_vendor,
                'followed_at' => $follow->followed_at->toISOString(),
                'is_following_me' => $customer ? UserFollow::where([
                    'follower_id' => $customer->id,
                    'following_id' => $followingUser->id,
                    'status' => UserFollow::STATUS_ACCEPTED
                ])->exists() : false,
                'is_followed_by_me' => $customer ? UserFollow::where([
                    'follower_id' => $followingUser->id,
                    'following_id' => $customer->id,
                    'status' => UserFollow::STATUS_ACCEPTED
                ])->exists() : false,
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب المتابَعين بنجاح',
            'data' => [
                'following' => $followingData,
                'pagination' => [
                    'current_page' => $following->currentPage(),
                    'last_page' => $following->lastPage(),
                    'total' => $following->total(),
                    'per_page' => $following->perPage()
                ]
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Search users
     */
    public function searchUsers(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        $request->validate([
            'query' => 'required|string|min:2|max:50'
        ]);

        $query = $request->query;

        $users = Customer::where(function ($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")
              ->orWhere('email', 'LIKE', "%{$query}%");
        })
        ->where('id', '!=', $customer?->id) // استبعاد المستخدم الحالي
        ->limit(20)
        ->get();

        $usersData = $users->map(function ($user) use ($customer) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'avatar' => $user->avatar_url,
                'is_vendor' => $user->is_vendor,
                'is_following' => $customer ? UserFollow::where([
                    'follower_id' => $customer->id,
                    'following_id' => $user->id,
                    'status' => UserFollow::STATUS_ACCEPTED
                ])->exists() : false,
                'followers_count' => UserFollow::where('following_id', $user->id)->accepted()->count(),
                'following_count' => UserFollow::where('follower_id', $user->id)->accepted()->count(),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم البحث بنجاح',
            'data' => $usersData,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get user profile with stats
     */
    public function getUserProfile(int $userId): JsonResponse
    {
        $customer = auth('sanctum')->user();

        $user = Customer::find($userId);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود',
                'error' => 'User not found',
            ], 404);
        }

        $followersCount = UserFollow::where('following_id', $userId)->accepted()->count();
        $followingCount = UserFollow::where('follower_id', $userId)->accepted()->count();
        $reelsCount = \Botble\VendorReels\Models\VendorReel::whereHas('store', function ($query) use ($userId) {
            $query->where('customer_id', $userId);
        })->where('status', 'published')->count();

        $isFollowing = false;
        $isFollowedBy = false;

        if ($customer && $customer->id !== $userId) {
            $isFollowing = UserFollow::where([
                'follower_id' => $customer->id,
                'following_id' => $userId,
                'status' => UserFollow::STATUS_ACCEPTED
            ])->exists();

            $isFollowedBy = UserFollow::where([
                'follower_id' => $userId,
                'following_id' => $customer->id,
                'status' => UserFollow::STATUS_ACCEPTED
            ])->exists();
        }

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الملف الشخصي بنجاح',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'avatar' => $user->avatar_url,
                'is_vendor' => $user->is_vendor,
                'created_at' => $user->created_at->toISOString(),
                'stats' => [
                    'followers_count' => $followersCount,
                    'following_count' => $followingCount,
                    'reels_count' => $reelsCount,
                ],
                'relationship' => [
                    'is_following' => $isFollowing,
                    'is_followed_by' => $isFollowedBy,
                    'is_self' => $customer && $customer->id === $userId,
                ],
                'store' => $user->is_vendor && $user->store ? [
                    'id' => $user->store->id,
                    'name' => $user->store->name,
                    'description' => $user->store->description,
                    'logo' => $user->store->logo_url,
                ] : null,
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get notifications
     */
    public function getNotifications(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $notifications = UserNotification::where('user_id', $customer->id)
            ->with('notifiable')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب الإشعارات بنجاح',
            'data' => NotificationResource::collection($notifications),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markNotificationAsRead(int $notificationId): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $notification = UserNotification::where([
            'id' => $notificationId,
            'user_id' => $customer->id
        ])->first();

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'الإشعار غير موجود',
                'error' => 'Notification not found',
            ], 404);
        }

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الإشعار كمقروء',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsAsRead(): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        UserNotification::where('user_id', $customer->id)
            ->unread()
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد جميع الإشعارات كمقروءة',
            'timestamp' => now()->toISOString(),
        ]);
    }
}
