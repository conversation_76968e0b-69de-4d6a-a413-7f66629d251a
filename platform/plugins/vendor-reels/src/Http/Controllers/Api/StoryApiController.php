<?php

namespace Botble\VendorReels\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON><PERSON>\VendorReels\Models\UserStory;
use Bo<PERSON>ble\VendorReels\Models\UserFollow;
use Botble\VendorReels\Http\Resources\StoryResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Botble\Media\Facades\RvMedia;

class StoryApiController extends BaseController
{
    /**
     * Get stories feed (following users + own stories)
     */
    public function index(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        // جلب معرفات المستخدمين المتابعين
        $followingIds = UserFollow::where('follower_id', $customer->id)
            ->accepted()
            ->pluck('following_id')
            ->toArray();

        // إضافة المستخدم الحالي للقائمة
        $followingIds[] = $customer->id;

        // جلب القصص النشطة
        $stories = UserStory::active()
            ->whereIn('user_id', $followingIds)
            ->where(function ($query) use ($customer) {
                $query->where('privacy', UserStory::PRIVACY_PUBLIC)
                      ->orWhere('privacy', UserStory::PRIVACY_FOLLOWERS)
                      ->orWhere('user_id', $customer->id); // قصص المستخدم نفسه
            })
            ->with(['user', 'store'])
            ->orderBy('created_at', 'desc')
            ->get();

        // تجميع القصص حسب المستخدم
        $groupedStories = $stories->groupBy('user_id')->map(function ($userStories) use ($customer) {
            $user = $userStories->first()->user;
            $hasUnseenStories = $userStories->contains(function ($story) use ($customer) {
                return !$story->views()->where('viewer_id', $customer->id)->exists();
            });

            return [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'avatar' => $user->avatar_url,
                    'is_vendor' => $user->is_vendor,
                ],
                'stories_count' => $userStories->count(),
                'has_unseen' => $hasUnseenStories,
                'latest_story_at' => $userStories->first()->created_at->toISOString(),
                'stories' => StoryResource::collection($userStories),
            ];
        })->values();

        return response()->json([
            'success' => true,
            'message' => 'تم جلب القصص بنجاح',
            'data' => $groupedStories,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Create new story
     */
    public function store(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'content' => 'nullable|string|max:500',
            'media' => 'nullable|file|max:50000', // 50MB
            'media_type' => 'required|in:text,image,video',
            'background_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'text_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'duration' => 'nullable|integer|min:5|max:30',
            'privacy' => 'nullable|in:public,followers,close_friends,private',
            'store_id' => 'nullable|exists:mp_stores,id'
        ]);

        try {
            $mediaUrl = null;

            // رفع الملف إذا كان موجوداً
            if ($request->hasFile('media') && in_array($request->media_type, ['image', 'video'])) {
                $file = $request->file('media');
                
                // التحقق من نوع الملف
                $allowedMimes = [
                    'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                    'video' => ['video/mp4', 'video/mov', 'video/avi']
                ];

                if (!in_array($file->getMimeType(), $allowedMimes[$request->media_type])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'نوع الملف غير مدعوم',
                        'error' => 'Unsupported file type',
                    ], 400);
                }

                $uploadedFile = RvMedia::handleUpload($file, 0, 'stories');
                
                if ($uploadedFile['error']) {
                    return response()->json([
                        'success' => false,
                        'message' => 'فشل في رفع الملف',
                        'error' => $uploadedFile['message'],
                    ], 500);
                }

                $mediaUrl = $uploadedFile['data']->url;
            }

            // إنشاء القصة
            $story = UserStory::create([
                'user_id' => $customer->id,
                'store_id' => $request->store_id,
                'content' => $request->content,
                'media_url' => $mediaUrl,
                'media_type' => $request->media_type,
                'background_color' => $request->background_color ?? '#000000',
                'text_color' => $request->text_color ?? '#FFFFFF',
                'duration' => $request->duration ?? 15,
                'privacy' => $request->privacy ?? UserStory::PRIVACY_FOLLOWERS,
                'expires_at' => now()->addHours(24), // تنتهي بعد 24 ساعة
            ]);

            $story->load(['user', 'store']);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء القصة بنجاح',
                'data' => new StoryResource($story),
                'timestamp' => now()->toISOString(),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء القصة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific story
     */
    public function show(UserStory $story): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        // التحقق من إمكانية المشاهدة
        if (!$story->canView($customer->id)) {
            return response()->json([
                'success' => false,
                'message' => 'غير مسموح لك بمشاهدة هذه القصة',
                'error' => 'Access denied',
            ], 403);
        }

        // تسجيل المشاهدة
        $story->addView($customer->id, request()->ip());

        $story->load(['user', 'store', 'views.viewer']);

        return response()->json([
            'success' => true,
            'message' => 'تم جلب القصة بنجاح',
            'data' => new StoryResource($story),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get user stories
     */
    public function userStories(Request $request, int $userId): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $stories = UserStory::active()
            ->byUser($userId)
            ->where(function ($query) use ($customer, $userId) {
                if ($customer->id === $userId) {
                    // المستخدم يشاهد قصصه الخاصة
                    return;
                }

                $query->where('privacy', UserStory::PRIVACY_PUBLIC);
                
                // إذا كان يتابع المستخدم، يمكنه رؤية قصص المتابعين
                $isFollowing = UserFollow::where([
                    'follower_id' => $customer->id,
                    'following_id' => $userId,
                    'status' => UserFollow::STATUS_ACCEPTED
                ])->exists();

                if ($isFollowing) {
                    $query->orWhere('privacy', UserStory::PRIVACY_FOLLOWERS);
                }
            })
            ->with(['user', 'store'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'تم جلب قصص المستخدم بنجاح',
            'data' => StoryResource::collection($stories),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Delete story
     */
    public function destroy(UserStory $story): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if ($story->user_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'غير مسموح لك بحذف هذه القصة',
                'error' => 'Access denied',
            ], 403);
        }

        // حذف الملف المرفق إذا وجد
        if ($story->media_url && in_array($story->media_type, [UserStory::MEDIA_TYPE_IMAGE, UserStory::MEDIA_TYPE_VIDEO])) {
            try {
                RvMedia::deleteFile($story->media_url);
            } catch (\Exception $e) {
                // تجاهل أخطاء حذف الملف
            }
        }

        $story->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف القصة بنجاح',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get story viewers
     */
    public function viewers(UserStory $story): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        if ($story->user_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'غير مسموح لك بمشاهدة قائمة المشاهدين',
                'error' => 'Access denied',
            ], 403);
        }

        $viewers = $story->views()
            ->with('viewer')
            ->orderBy('viewed_at', 'desc')
            ->get()
            ->map(function ($view) {
                return [
                    'id' => $view->viewer->id,
                    'name' => $view->viewer->name,
                    'avatar' => $view->viewer->avatar_url,
                    'viewed_at' => $view->viewed_at->toISOString(),
                ];
            });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب قائمة المشاهدين بنجاح',
            'data' => $viewers,
            'timestamp' => now()->toISOString(),
        ]);
    }
}
