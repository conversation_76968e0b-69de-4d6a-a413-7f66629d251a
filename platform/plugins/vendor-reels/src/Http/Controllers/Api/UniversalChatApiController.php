<?php

namespace Bo<PERSON>ble\VendorReels\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\VendorReels\Models\Conversation;
use Bo<PERSON>ble\VendorReels\Models\Message;
use Bo<PERSON>ble\VendorReels\Models\UserBlock;
use Botble\VendorReels\Http\Resources\ConversationResource;
use Botble\VendorReels\Http\Resources\MessageResource;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class UniversalChatApiController extends BaseController
{
    /**
     * Start conversation with any user type
     */
    public function startConversation(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'participant_id' => 'required|exists:ec_customers,id',
            'initial_message' => 'nullable|string|max:1000'
        ]);

        $participantId = $request->participant_id;

        // لا يمكن بدء محادثة مع النفس
        if ($customer->id === $participantId) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك بدء محادثة مع نفسك',
                'error' => 'Cannot chat with yourself',
            ], 400);
        }

        // التحقق من عدم وجود حظر
        $isBlocked = UserBlock::where(function ($query) use ($customer, $participantId) {
            $query->where('blocker_id', $customer->id)->where('blocked_id', $participantId);
        })->orWhere(function ($query) use ($customer, $participantId) {
            $query->where('blocker_id', $participantId)->where('blocked_id', $customer->id);
        })->exists();

        if ($isBlocked) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن بدء محادثة مع هذا المستخدم',
                'error' => 'User is blocked',
            ], 403);
        }

        // البحث عن محادثة موجودة
        $existingConversation = Conversation::where('type', Conversation::TYPE_PRIVATE)
            ->whereHas('participants', function ($query) use ($customer) {
                $query->where('customer_id', $customer->id)->whereNull('left_at');
            })
            ->whereHas('participants', function ($query) use ($participantId) {
                $query->where('customer_id', $participantId)->whereNull('left_at');
            })
            ->first();

        if ($existingConversation) {
            return response()->json([
                'success' => true,
                'message' => 'المحادثة موجودة مسبقاً',
                'data' => new ConversationResource($existingConversation->load(['participants', 'lastMessage'])),
                'timestamp' => now()->toISOString(),
            ]);
        }

        // إنشاء محادثة جديدة
        DB::beginTransaction();
        try {
            $participant = Customer::find($participantId);
            
            // تحديد نوع المحادثة بناءً على نوع المستخدمين
            $conversationType = $this->determineConversationType($customer, $participant);
            
            $conversation = Conversation::create([
                'type' => $conversationType,
                'title' => $this->generateConversationTitle($customer, $participant),
                'is_group' => false,
                'created_by' => $customer->id,
                'store_id' => $this->getRelevantStoreId($customer, $participant),
            ]);

            // إضافة المشاركين
            $conversation->addParticipant($customer->id);
            $conversation->addParticipant($participantId);

            // إرسال رسالة أولية إذا تم توفيرها
            if ($request->initial_message) {
                $message = Message::create([
                    'conversation_id' => $conversation->id,
                    'sender_id' => $customer->id,
                    'content' => $request->initial_message,
                    'type' => Message::TYPE_TEXT,
                ]);

                $conversation->updateLastMessage($message);
            }

            DB::commit();

            $conversation->load(['participants', 'lastMessage']);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المحادثة بنجاح',
                'data' => new ConversationResource($conversation),
                'timestamp' => now()->toISOString(),
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء المحادثة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get online users for chat
     */
    public function getOnlineUsers(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        // جلب المستخدمين المتصلين (آخر نشاط خلال 5 دقائق)
        $onlineUsers = Customer::where('last_seen_at', '>=', now()->subMinutes(5))
            ->where('id', '!=', $customer->id)
            ->whereNotIn('id', function ($query) use ($customer) {
                // استبعاد المستخدمين المحظورين
                $query->select('blocked_id')
                      ->from('user_blocks')
                      ->where('blocker_id', $customer->id);
            })
            ->whereNotIn('id', function ($query) use ($customer) {
                // استبعاد المستخدمين الذين حظروني
                $query->select('blocker_id')
                      ->from('user_blocks')
                      ->where('blocked_id', $customer->id);
            })
            ->orderBy('last_seen_at', 'desc')
            ->limit(50)
            ->get();

        $usersData = $onlineUsers->map(function ($user) use ($customer) {
            // التحقق من وجود محادثة
            $hasConversation = Conversation::where('type', Conversation::TYPE_PRIVATE)
                ->whereHas('participants', function ($query) use ($customer) {
                    $query->where('customer_id', $customer->id)->whereNull('left_at');
                })
                ->whereHas('participants', function ($query) use ($user) {
                    $query->where('customer_id', $user->id)->whereNull('left_at');
                })
                ->exists();

            return [
                'id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->avatar_url,
                'is_vendor' => $user->is_vendor,
                'last_seen_at' => $user->last_seen_at->toISOString(),
                'is_online' => true,
                'has_conversation' => $hasConversation,
                'user_type' => $this->getUserType($user),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب المستخدمين المتصلين بنجاح',
            'data' => $usersData,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get suggested users to chat with
     */
    public function getSuggestedUsers(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $suggestedUsers = collect();

        // 1. المستخدمين المتابعين
        if ($customer->following) {
            $followingUsers = $customer->following()
                ->whereNotIn('id', function ($query) use ($customer) {
                    $query->select('blocked_id')->from('user_blocks')->where('blocker_id', $customer->id);
                })
                ->limit(10)
                ->get();
            
            $suggestedUsers = $suggestedUsers->merge($followingUsers);
        }

        // 2. البائعين النشطين
        $activeVendors = Customer::where('is_vendor', true)
            ->where('id', '!=', $customer->id)
            ->whereHas('store')
            ->whereNotIn('id', function ($query) use ($customer) {
                $query->select('blocked_id')->from('user_blocks')->where('blocker_id', $customer->id);
            })
            ->limit(5)
            ->get();

        $suggestedUsers = $suggestedUsers->merge($activeVendors);

        // 3. مستخدمين عشوائيين
        $randomUsers = Customer::where('id', '!=', $customer->id)
            ->whereNotIn('id', $suggestedUsers->pluck('id'))
            ->whereNotIn('id', function ($query) use ($customer) {
                $query->select('blocked_id')->from('user_blocks')->where('blocker_id', $customer->id);
            })
            ->inRandomOrder()
            ->limit(5)
            ->get();

        $suggestedUsers = $suggestedUsers->merge($randomUsers);

        $usersData = $suggestedUsers->unique('id')->take(20)->map(function ($user) use ($customer) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->avatar_url,
                'is_vendor' => $user->is_vendor,
                'user_type' => $this->getUserType($user),
                'suggestion_reason' => $this->getSuggestionReason($customer, $user),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب المستخدمين المقترحين بنجاح',
            'data' => $usersData,
            'timestamp' => now()->toISOString(),
        ]);
    }

    private function determineConversationType($user1, $user2): string
    {
        if ($user1->is_vendor && $user2->is_vendor) {
            return Conversation::TYPE_VENDOR_VENDOR;
        } elseif ($user1->is_vendor || $user2->is_vendor) {
            return Conversation::TYPE_STORE_CUSTOMER;
        } else {
            return Conversation::TYPE_CUSTOMER_CUSTOMER;
        }
    }

    private function generateConversationTitle($user1, $user2): string
    {
        return "محادثة بين {$user1->name} و {$user2->name}";
    }

    private function getRelevantStoreId($user1, $user2): ?int
    {
        if ($user1->is_vendor && $user1->store) {
            return $user1->store->id;
        } elseif ($user2->is_vendor && $user2->store) {
            return $user2->store->id;
        }
        return null;
    }

    private function getUserType($user): string
    {
        if ($user->is_vendor) {
            return 'vendor';
        }
        // يمكن إضافة فحص للإدمن هنا
        return 'customer';
    }

    private function getSuggestionReason($customer, $user): string
    {
        if ($customer->following && $customer->following->contains($user)) {
            return 'تتابع هذا المستخدم';
        } elseif ($user->is_vendor) {
            return 'بائع نشط';
        } else {
            return 'مستخدم مقترح';
        }
    }
}
