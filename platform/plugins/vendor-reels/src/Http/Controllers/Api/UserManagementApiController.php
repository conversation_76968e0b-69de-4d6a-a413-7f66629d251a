<?php

namespace Bo<PERSON>ble\VendorReels\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON><PERSON>\VendorReels\Models\UserBlock;
use Bo<PERSON><PERSON>\VendorReels\Models\CloseFriend;
use Bo<PERSON>ble\VendorReels\Models\UserFollow;
use Botble\VendorReels\Models\UserNotification;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class UserManagementApiController extends BaseController
{
    /**
     * Block a user
     */
    public function blockUser(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'user_id' => 'required|exists:ec_customers,id',
            'reason' => 'nullable|string|max:255'
        ]);

        $targetUserId = $request->user_id;

        // لا يمكن حظر النفس
        if ($customer->id === $targetUserId) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك حظر نفسك',
                'error' => 'Cannot block yourself',
            ], 400);
        }

        // التحقق من وجود حظر سابق
        $existingBlock = UserBlock::where([
            'blocker_id' => $customer->id,
            'blocked_id' => $targetUserId
        ])->first();

        if ($existingBlock) {
            return response()->json([
                'success' => false,
                'message' => 'هذا المستخدم محظور بالفعل',
                'error' => 'User already blocked',
            ], 400);
        }

        DB::beginTransaction();
        try {
            // إنشاء الحظر
            UserBlock::create([
                'blocker_id' => $customer->id,
                'blocked_id' => $targetUserId,
                'reason' => $request->reason,
                'blocked_at' => now()
            ]);

            // إلغاء المتابعة إذا كانت موجودة
            UserFollow::where([
                'follower_id' => $customer->id,
                'following_id' => $targetUserId
            ])->delete();

            UserFollow::where([
                'follower_id' => $targetUserId,
                'following_id' => $customer->id
            ])->delete();

            // إزالة من الأصدقاء المقربين
            CloseFriend::where([
                'user_id' => $customer->id,
                'friend_id' => $targetUserId
            ])->delete();

            CloseFriend::where([
                'user_id' => $targetUserId,
                'friend_id' => $customer->id
            ])->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حظر المستخدم بنجاح',
                'timestamp' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حظر المستخدم',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Unblock a user
     */
    public function unblockUser(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'user_id' => 'required|exists:ec_customers,id'
        ]);

        $targetUserId = $request->user_id;

        $block = UserBlock::where([
            'blocker_id' => $customer->id,
            'blocked_id' => $targetUserId
        ])->first();

        if (!$block) {
            return response()->json([
                'success' => false,
                'message' => 'هذا المستخدم غير محظور',
                'error' => 'User not blocked',
            ], 400);
        }

        $block->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء حظر المستخدم بنجاح',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get blocked users list
     */
    public function getBlockedUsers(): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $blockedUsers = UserBlock::where('blocker_id', $customer->id)
            ->with('blocked')
            ->orderBy('blocked_at', 'desc')
            ->paginate(20);

        $blockedUsersData = $blockedUsers->map(function ($block) {
            return [
                'id' => $block->blocked->id,
                'name' => $block->blocked->name,
                'avatar' => $block->blocked->avatar_url,
                'reason' => $block->reason,
                'blocked_at' => $block->blocked_at->toISOString(),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب قائمة المحظورين بنجاح',
            'data' => [
                'blocked_users' => $blockedUsersData,
                'pagination' => [
                    'current_page' => $blockedUsers->currentPage(),
                    'last_page' => $blockedUsers->lastPage(),
                    'total' => $blockedUsers->total(),
                    'per_page' => $blockedUsers->perPage()
                ]
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Add user to close friends
     */
    public function addCloseFriend(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'user_id' => 'required|exists:ec_customers,id'
        ]);

        $targetUserId = $request->user_id;

        // لا يمكن إضافة النفس
        if ($customer->id === $targetUserId) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك إضافة نفسك للأصدقاء المقربين',
                'error' => 'Cannot add yourself',
            ], 400);
        }

        // التحقق من المتابعة
        $isFollowing = UserFollow::where([
            'follower_id' => $customer->id,
            'following_id' => $targetUserId,
            'status' => UserFollow::STATUS_ACCEPTED
        ])->exists();

        if (!$isFollowing) {
            return response()->json([
                'success' => false,
                'message' => 'يجب أن تتابع المستخدم أولاً',
                'error' => 'Must follow user first',
            ], 400);
        }

        // التحقق من وجود إضافة سابقة
        $existingFriend = CloseFriend::where([
            'user_id' => $customer->id,
            'friend_id' => $targetUserId
        ])->first();

        if ($existingFriend) {
            return response()->json([
                'success' => false,
                'message' => 'هذا المستخدم في قائمة الأصدقاء المقربين بالفعل',
                'error' => 'Already in close friends',
            ], 400);
        }

        CloseFriend::create([
            'user_id' => $customer->id,
            'friend_id' => $targetUserId,
            'added_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة المستخدم للأصدقاء المقربين بنجاح',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Remove user from close friends
     */
    public function removeCloseFriend(Request $request): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $request->validate([
            'user_id' => 'required|exists:ec_customers,id'
        ]);

        $targetUserId = $request->user_id;

        $closeFriend = CloseFriend::where([
            'user_id' => $customer->id,
            'friend_id' => $targetUserId
        ])->first();

        if (!$closeFriend) {
            return response()->json([
                'success' => false,
                'message' => 'هذا المستخدم ليس في قائمة الأصدقاء المقربين',
                'error' => 'Not in close friends',
            ], 400);
        }

        $closeFriend->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم إزالة المستخدم من الأصدقاء المقربين بنجاح',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get close friends list
     */
    public function getCloseFriends(): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $closeFriends = CloseFriend::where('user_id', $customer->id)
            ->with('friend')
            ->orderBy('added_at', 'desc')
            ->paginate(20);

        $closeFriendsData = $closeFriends->map(function ($closeFriend) {
            return [
                'id' => $closeFriend->friend->id,
                'name' => $closeFriend->friend->name,
                'avatar' => $closeFriend->friend->avatar_url,
                'is_vendor' => $closeFriend->friend->is_vendor,
                'added_at' => $closeFriend->added_at->toISOString(),
                'is_online' => $closeFriend->friend->last_seen_at && 
                              $closeFriend->friend->last_seen_at->diffInMinutes() < 5,
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'تم جلب قائمة الأصدقاء المقربين بنجاح',
            'data' => [
                'close_friends' => $closeFriendsData,
                'pagination' => [
                    'current_page' => $closeFriends->currentPage(),
                    'last_page' => $closeFriends->lastPage(),
                    'total' => $closeFriends->total(),
                    'per_page' => $closeFriends->perPage()
                ]
            ],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Update user online status
     */
    public function updateOnlineStatus(): JsonResponse
    {
        $customer = auth('sanctum')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً',
                'error' => 'Authentication required',
            ], 401);
        }

        $customer->update([
            'last_seen_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الاتصال',
            'timestamp' => now()->toISOString(),
        ]);
    }
}
