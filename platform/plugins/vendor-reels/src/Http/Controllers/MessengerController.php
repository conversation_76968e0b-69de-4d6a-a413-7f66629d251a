<?php

namespace Bo<PERSON>ble\VendorReels\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\VendorReels\Models\Conversation;
use Botble\VendorReels\Models\UserStory;
use Bo<PERSON>ble\VendorReels\Models\VendorReel;
use Illuminate\Http\Request;

class MessengerController extends BaseController
{
    /**
     * Display messenger interface
     */
    public function index()
    {
        $this->pageTitle('الماسنجر');

        return view('plugins/vendor-reels::messenger.index');
    }

    /**
     * Display stories interface
     */
    public function stories()
    {
        $this->pageTitle('القصص');

        // جلب القصص النشطة
        $stories = UserStory::active()
            ->with(['user', 'store'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('plugins/vendor-reels::stories.index', compact('stories'));
    }

    /**
     * Display reels interface
     */
    public function reels()
    {
        $this->pageTitle('الريلز');

        // جلب الريلز المنشورة
        $reels = VendorReel::where('status', 'published')
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('plugins/vendor-reels::reels.index', compact('reels'));
    }

    /**
     * Display user profile
     */
    public function profile($userId)
    {
        $user = \Botble\Ecommerce\Models\Customer::findOrFail($userId);
        
        $this->pageTitle("الملف الشخصي - {$user->name}");

        // إحصائيات المستخدم
        $stats = [
            'followers_count' => $user->followers()->count(),
            'following_count' => $user->following()->count(),
            'reels_count' => 0,
            'stories_count' => $user->stories()->active()->count(),
        ];

        // إذا كان بائع، احسب عدد الريلز
        if ($user->is_vendor && $user->store) {
            $stats['reels_count'] = VendorReel::where('store_id', $user->store->id)
                ->where('status', 'published')
                ->count();
        }

        // الريلز الأخيرة
        $recentReels = collect();
        if ($user->is_vendor && $user->store) {
            $recentReels = VendorReel::where('store_id', $user->store->id)
                ->where('status', 'published')
                ->orderBy('created_at', 'desc')
                ->limit(6)
                ->get();
        }

        // القصص النشطة
        $activeStories = $user->stories()
            ->active()
            ->orderBy('created_at', 'desc')
            ->get();

        return view('plugins/vendor-reels::profile.show', compact(
            'user', 
            'stats', 
            'recentReels', 
            'activeStories'
        ));
    }

    /**
     * Display social feed
     */
    public function feed()
    {
        $this->pageTitle('الصفحة الرئيسية');

        $customer = auth('customer')->user();
        
        if (!$customer) {
            return redirect()->route('customer.login');
        }

        // جلب الريلز من المتابعين
        $followingIds = $customer->following()->pluck('id')->toArray();
        $followingIds[] = $customer->id; // إضافة المستخدم نفسه

        $reels = VendorReel::where('status', 'published')
            ->whereHas('store', function ($query) use ($followingIds) {
                $query->whereIn('customer_id', $followingIds);
            })
            ->with(['store', 'products'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // جلب القصص من المتابعين
        $stories = UserStory::active()
            ->whereIn('user_id', $followingIds)
            ->with(['user', 'store'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy('user_id');

        return view('plugins/vendor-reels::feed.index', compact('reels', 'stories'));
    }

    /**
     * Display notifications
     */
    public function notifications()
    {
        $this->pageTitle('الإشعارات');

        $customer = auth('customer')->user();
        
        if (!$customer) {
            return redirect()->route('customer.login');
        }

        $notifications = $customer->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('plugins/vendor-reels::notifications.index', compact('notifications'));
    }

    /**
     * Display search results
     */
    public function search(Request $request)
    {
        $this->pageTitle('البحث');

        $query = $request->get('q', '');
        $type = $request->get('type', 'all'); // all, users, reels, stories

        $results = [
            'users' => collect(),
            'reels' => collect(),
            'stories' => collect(),
        ];

        if (strlen($query) >= 2) {
            // البحث في المستخدمين
            if (in_array($type, ['all', 'users'])) {
                $results['users'] = \Botble\Ecommerce\Models\Customer::where('name', 'LIKE', "%{$query}%")
                    ->orWhere('email', 'LIKE', "%{$query}%")
                    ->limit(20)
                    ->get();
            }

            // البحث في الريلز
            if (in_array($type, ['all', 'reels'])) {
                $results['reels'] = VendorReel::where('status', 'published')
                    ->where(function ($q) use ($query) {
                        $q->where('title', 'LIKE', "%{$query}%")
                          ->orWhere('description', 'LIKE', "%{$query}%");
                    })
                    ->with(['store'])
                    ->limit(20)
                    ->get();
            }

            // البحث في القصص
            if (in_array($type, ['all', 'stories'])) {
                $results['stories'] = UserStory::active()
                    ->where('content', 'LIKE', "%{$query}%")
                    ->where('privacy', 'public')
                    ->with(['user'])
                    ->limit(20)
                    ->get();
            }
        }

        return view('plugins/vendor-reels::search.results', compact('query', 'type', 'results'));
    }

    /**
     * Display explore page
     */
    public function explore()
    {
        $this->pageTitle('استكشاف');

        // الريلز الشائعة
        $popularReels = VendorReel::where('status', 'published')
            ->orderBy('views_count', 'desc')
            ->orderBy('likes_count', 'desc')
            ->with(['store'])
            ->limit(12)
            ->get();

        // البائعين النشطين
        $activeVendors = \Botble\Ecommerce\Models\Customer::where('is_vendor', true)
            ->whereHas('store')
            ->withCount(['followers'])
            ->orderBy('followers_count', 'desc')
            ->limit(8)
            ->get();

        // القصص العامة
        $publicStories = UserStory::active()
            ->where('privacy', 'public')
            ->with(['user'])
            ->orderBy('views_count', 'desc')
            ->limit(10)
            ->get();

        return view('plugins/vendor-reels::explore.index', compact(
            'popularReels',
            'activeVendors', 
            'publicStories'
        ));
    }
}
