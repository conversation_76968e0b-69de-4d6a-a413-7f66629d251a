<?php

namespace Botble\VendorReels\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'title' => $this->title,
            'message' => $this->message,
            'data' => $this->data,
            'action_url' => $this->action_url,
            'is_read' => $this->is_read,
            'read_at' => $this->read_at?->toISOString(),
            'priority' => $this->priority,
            'created_at' => $this->created_at->toISOString(),
            'time_ago' => $this->time_ago,
            'icon' => $this->icon,
            'color' => $this->color,
            'notifiable' => $this->getNotifiableData(),
        ];
    }

    private function getNotifiableData(): ?array
    {
        if (!$this->notifiable) {
            return null;
        }

        switch ($this->notifiable_type) {
            case 'Botble\Ecommerce\Models\Customer':
                return [
                    'type' => 'user',
                    'id' => $this->notifiable->id,
                    'name' => $this->notifiable->name,
                    'avatar' => $this->notifiable->avatar_url,
                ];

            case 'Botble\VendorReels\Models\VendorReel':
                return [
                    'type' => 'reel',
                    'id' => $this->notifiable->id,
                    'title' => $this->notifiable->title,
                    'thumbnail' => $this->notifiable->thumbnail_url,
                ];

            case 'Botble\VendorReels\Models\UserStory':
                return [
                    'type' => 'story',
                    'id' => $this->notifiable->id,
                    'media_url' => $this->notifiable->media_url,
                    'media_type' => $this->notifiable->media_type,
                ];

            case 'Botble\VendorReels\Models\Message':
                return [
                    'type' => 'message',
                    'id' => $this->notifiable->id,
                    'content' => $this->notifiable->getPreviewText(),
                    'conversation_id' => $this->notifiable->conversation_id,
                ];

            default:
                return [
                    'type' => 'unknown',
                    'id' => $this->notifiable->id,
                ];
        }
    }
}
