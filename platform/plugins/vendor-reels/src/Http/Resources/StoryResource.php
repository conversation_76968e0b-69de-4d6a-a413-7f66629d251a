<?php

namespace Botble\VendorReels\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StoryResource extends JsonResource
{
    public function toArray($request): array
    {
        $customer = auth('sanctum')->user();
        
        return [
            'id' => $this->id,
            'content' => $this->content,
            'media_url' => $this->media_url,
            'media_type' => $this->media_type,
            'background_color' => $this->background_color,
            'text_color' => $this->text_color,
            'duration' => $this->duration,
            'views_count' => $this->views_count,
            'privacy' => $this->privacy,
            'created_at' => $this->created_at->toISOString(),
            'expires_at' => $this->expires_at->toISOString(),
            'time_remaining' => $this->getTimeRemaining(),
            'is_expired' => $this->isExpired(),
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'avatar' => $this->user->avatar_url,
                    'is_vendor' => $this->user->is_vendor,
                ];
            }),
            'store' => $this->whenLoaded('store', function () {
                return $this->store ? [
                    'id' => $this->store->id,
                    'name' => $this->store->name,
                    'logo' => $this->store->logo_url,
                ] : null;
            }),
            'is_viewed_by_me' => $customer ? $this->views()->where('viewer_id', $customer->id)->exists() : false,
            'can_delete' => $customer ? $this->user_id === $customer->id : false,
            'viewers' => $this->whenLoaded('views', function () use ($customer) {
                // إظهار المشاهدين فقط لصاحب القصة
                if ($customer && $this->user_id === $customer->id) {
                    return $this->views->map(function ($view) {
                        return [
                            'id' => $view->viewer->id,
                            'name' => $view->viewer->name,
                            'avatar' => $view->viewer->avatar_url,
                            'viewed_at' => $view->viewed_at->toISOString(),
                        ];
                    });
                }
                return [];
            }),
        ];
    }
}
