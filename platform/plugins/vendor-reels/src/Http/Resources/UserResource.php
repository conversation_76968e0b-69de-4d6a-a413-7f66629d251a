<?php

namespace Botble\VendorReels\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Botble\VendorReels\Models\UserFollow;

class UserResource extends JsonResource
{
    public function toArray($request): array
    {
        $customer = auth('sanctum')->user();
        
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'avatar' => $this->avatar_url,
            'is_vendor' => $this->is_vendor,
            'created_at' => $this->created_at->toISOString(),
            'last_seen_at' => $this->last_seen_at?->toISOString(),
            'is_online' => $this->last_seen_at && $this->last_seen_at->diffInMinutes() < 5,
            'stats' => [
                'followers_count' => $this->getFollowersCount(),
                'following_count' => $this->getFollowingCount(),
                'reels_count' => $this->getReelsCount(),
                'stories_count' => $this->getActiveStoriesCount(),
            ],
            'relationship' => $this->getRelationshipData($customer),
            'store' => $this->when($this->is_vendor && $this->store, function () {
                return [
                    'id' => $this->store->id,
                    'name' => $this->store->name,
                    'description' => $this->store->description,
                    'logo' => $this->store->logo_url,
                    'verified' => $this->store->is_verified ?? false,
                ];
            }),
        ];
    }

    private function getFollowersCount(): int
    {
        return UserFollow::where('following_id', $this->id)->accepted()->count();
    }

    private function getFollowingCount(): int
    {
        return UserFollow::where('follower_id', $this->id)->accepted()->count();
    }

    private function getReelsCount(): int
    {
        if (!$this->is_vendor || !$this->store) {
            return 0;
        }

        return \Botble\VendorReels\Models\VendorReel::where('store_id', $this->store->id)
            ->where('status', 'published')
            ->count();
    }

    private function getActiveStoriesCount(): int
    {
        return \Botble\VendorReels\Models\UserStory::active()
            ->byUser($this->id)
            ->count();
    }

    private function getRelationshipData($customer): array
    {
        if (!$customer || $customer->id === $this->id) {
            return [
                'is_following' => false,
                'is_followed_by' => false,
                'is_self' => $customer && $customer->id === $this->id,
                'is_blocked' => false,
                'is_close_friend' => false,
            ];
        }

        $isFollowing = UserFollow::where([
            'follower_id' => $customer->id,
            'following_id' => $this->id,
            'status' => UserFollow::STATUS_ACCEPTED
        ])->exists();

        $isFollowedBy = UserFollow::where([
            'follower_id' => $this->id,
            'following_id' => $customer->id,
            'status' => UserFollow::STATUS_ACCEPTED
        ])->exists();

        $isBlocked = \Botble\VendorReels\Models\UserBlock::where([
            'blocker_id' => $customer->id,
            'blocked_id' => $this->id
        ])->exists();

        $isCloseFriend = \Botble\VendorReels\Models\CloseFriend::where([
            'user_id' => $customer->id,
            'friend_id' => $this->id
        ])->exists();

        return [
            'is_following' => $isFollowing,
            'is_followed_by' => $isFollowedBy,
            'is_self' => false,
            'is_blocked' => $isBlocked,
            'is_close_friend' => $isCloseFriend,
        ];
    }
}
