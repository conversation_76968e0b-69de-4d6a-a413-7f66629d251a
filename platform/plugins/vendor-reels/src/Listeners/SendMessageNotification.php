<?php

namespace Botble\VendorReels\Listeners;

use Bo<PERSON>ble\VendorReels\Events\MessageSent;
use Botble\VendorReels\Notifications\NewMessageNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendMessageNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(MessageSent $event): void
    {
        $message = $event->message;
        $conversation = $message->conversation;
        
        // إرسال إشعار لجميع المشاركين عدا المرسل
        $participants = $conversation->participants()
            ->where('customer_id', '!=', $message->sender_id)
            ->whereNull('left_at')
            ->get();

        foreach ($participants as $participant) {
            // تجاهل المشاركين المكتومين
            if ($participant->pivot->is_muted) {
                continue;
            }

            try {
                $participant->notify(new NewMessageNotification($message));
            } catch (\Exception $e) {
                // تسجيل الخطأ وتجاهله
                logger()->error('Failed to send message notification', [
                    'participant_id' => $participant->id,
                    'message_id' => $message->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
