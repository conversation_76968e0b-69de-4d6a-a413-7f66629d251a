<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CloseFriend extends BaseModel
{
    protected $table = 'close_friends';

    protected $fillable = [
        'user_id',
        'friend_id',
        'added_at',
    ];

    protected $casts = [
        'added_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'user_id');
    }

    public function friend(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'friend_id');
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByFriend($query, $friendId)
    {
        return $query->where('friend_id', $friendId);
    }
}
