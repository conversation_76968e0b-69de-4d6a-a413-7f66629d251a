<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Botble\Marketplace\Models\Store;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Conversation extends BaseModel
{
    protected $table = 'conversations';

    protected $fillable = [
        'type',
        'title',
        'last_message_id',
        'last_message_at',
        'is_group',
        'created_by',
        'store_id',
    ];

    protected $casts = [
        'is_group' => 'boolean',
        'last_message_at' => 'datetime',
    ];

    const TYPE_PRIVATE = 'private';
    const TYPE_GROUP = 'group';
    const TYPE_SUPPORT = 'support';
    const TYPE_STORE_CUSTOMER = 'store_customer';
    const TYPE_CUSTOMER_CUSTOMER = 'customer_customer';
    const TYPE_VENDOR_VENDOR = 'vendor_vendor';
    const TYPE_ADMIN_CUSTOMER = 'admin_customer';
    const TYPE_ADMIN_VENDOR = 'admin_vendor';

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class)->orderBy('created_at', 'desc');
    }

    public function lastMessage(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'last_message_id');
    }

    public function participants(): BelongsToMany
    {
        return $this->belongsToMany(Customer::class, 'conversation_participants', 'conversation_id', 'customer_id')
            ->withPivot(['joined_at', 'left_at', 'is_admin', 'is_muted'])
            ->withTimestamps();
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'created_by');
    }

    public function scopeForCustomer($query, $customerId)
    {
        return $query->whereHas('participants', function ($q) use ($customerId) {
            $q->where('customer_id', $customerId)
              ->whereNull('left_at');
        });
    }

    public function scopePrivate($query)
    {
        return $query->where('type', self::TYPE_PRIVATE);
    }

    public function scopeGroup($query)
    {
        return $query->where('type', self::TYPE_GROUP);
    }

    public function scopeStoreCustomer($query)
    {
        return $query->where('type', self::TYPE_STORE_CUSTOMER);
    }

    public function addParticipant($customerId, $isAdmin = false): void
    {
        $this->participants()->syncWithoutDetaching([
            $customerId => [
                'joined_at' => now(),
                'is_admin' => $isAdmin,
                'is_muted' => false
            ]
        ]);
    }

    public function removeParticipant($customerId): void
    {
        $this->participants()->updateExistingPivot($customerId, [
            'left_at' => now()
        ]);
    }

    public function updateLastMessage(Message $message): void
    {
        $this->update([
            'last_message_id' => $message->id,
            'last_message_at' => $message->created_at
        ]);
    }

    public function getUnreadCount($customerId): int
    {
        return $this->messages()
            ->where('sender_id', '!=', $customerId)
            ->whereDoesntHave('readReceipts', function ($query) use ($customerId) {
                $query->where('customer_id', $customerId);
            })
            ->count();
    }

    public function markAsRead($customerId): void
    {
        $unreadMessages = $this->messages()
            ->where('sender_id', '!=', $customerId)
            ->whereDoesntHave('readReceipts', function ($query) use ($customerId) {
                $query->where('customer_id', $customerId);
            })
            ->get();

        foreach ($unreadMessages as $message) {
            $message->markAsRead($customerId);
        }
    }

    public function isParticipant($customerId): bool
    {
        return $this->participants()
            ->where('customer_id', $customerId)
            ->whereNull('left_at')
            ->exists();
    }

    public function canSendMessage($customerId): bool
    {
        if (!$this->isParticipant($customerId)) {
            return false;
        }

        $participant = $this->participants()
            ->where('customer_id', $customerId)
            ->first();

        return $participant && !$participant->pivot->is_muted;
    }
}
