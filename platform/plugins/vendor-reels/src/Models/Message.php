<?php

namespace Botble\VendorReels\Models;

use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Botble\Media\Facades\RvMedia;

class Message extends BaseModel
{
    protected $table = 'messages';

    protected $fillable = [
        'conversation_id',
        'sender_id',
        'content',
        'type',
        'attachment_url',
        'attachment_type',
        'attachment_size',
        'reply_to_id',
        'is_edited',
        'edited_at',
        'messageable_type',
        'messageable_id',
    ];

    protected $casts = [
        'is_edited' => 'boolean',
        'edited_at' => 'datetime',
        'attachment_size' => 'integer',
    ];

    const TYPE_TEXT = 'text';
    const TYPE_IMAGE = 'image';
    const TYPE_VIDEO = 'video';
    const TYPE_AUDIO = 'audio';
    const TYPE_FILE = 'file';
    const TYPE_REEL = 'reel';
    const TYPE_PRODUCT = 'product';
    const TYPE_LOCATION = 'location';
    const TYPE_SYSTEM = 'system';

    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    public function sender(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'sender_id');
    }

    public function replyTo(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'reply_to_id');
    }

    public function replies(): HasMany
    {
        return $this->hasMany(Message::class, 'reply_to_id');
    }

    public function readReceipts(): HasMany
    {
        return $this->hasMany(MessageReadReceipt::class);
    }

    public function messageable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getAttachmentUrlAttribute($value): ?string
    {
        if ($value && in_array($this->type, [self::TYPE_IMAGE, self::TYPE_VIDEO, self::TYPE_AUDIO, self::TYPE_FILE])) {
            return RvMedia::getImageUrl($value);
        }

        return $value;
    }

    public function getFormattedSizeAttribute(): ?string
    {
        if (!$this->attachment_size) {
            return null;
        }

        $bytes = $this->attachment_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function markAsRead($customerId): void
    {
        MessageReadReceipt::firstOrCreate([
            'message_id' => $this->id,
            'customer_id' => $customerId,
        ], [
            'read_at' => now()
        ]);
    }

    public function isReadBy($customerId): bool
    {
        return $this->readReceipts()
            ->where('customer_id', $customerId)
            ->exists();
    }

    public function getReadByCount(): int
    {
        return $this->readReceipts()->count();
    }

    public function edit($newContent): void
    {
        $this->update([
            'content' => $newContent,
            'is_edited' => true,
            'edited_at' => now()
        ]);
    }

    public function canEdit($customerId): bool
    {
        return $this->sender_id === $customerId && 
               $this->type === self::TYPE_TEXT &&
               $this->created_at->diffInMinutes() <= 15; // يمكن التعديل خلال 15 دقيقة
    }

    public function canDelete($customerId): bool
    {
        return $this->sender_id === $customerId || 
               $this->conversation->participants()
                   ->where('customer_id', $customerId)
                   ->where('is_admin', true)
                   ->exists();
    }

    public function scopeText($query)
    {
        return $query->where('type', self::TYPE_TEXT);
    }

    public function scopeMedia($query)
    {
        return $query->whereIn('type', [self::TYPE_IMAGE, self::TYPE_VIDEO, self::TYPE_AUDIO]);
    }

    public function scopeFiles($query)
    {
        return $query->where('type', self::TYPE_FILE);
    }

    public function scopeUnread($query, $customerId)
    {
        return $query->whereDoesntHave('readReceipts', function ($q) use ($customerId) {
            $q->where('customer_id', $customerId);
        });
    }

    public function getPreviewText(): string
    {
        switch ($this->type) {
            case self::TYPE_TEXT:
                return $this->content;
            case self::TYPE_IMAGE:
                return '📷 صورة';
            case self::TYPE_VIDEO:
                return '🎥 فيديو';
            case self::TYPE_AUDIO:
                return '🎵 تسجيل صوتي';
            case self::TYPE_FILE:
                return '📎 ملف';
            case self::TYPE_REEL:
                return '🎬 ريل';
            case self::TYPE_PRODUCT:
                return '🛍️ منتج';
            case self::TYPE_LOCATION:
                return '📍 موقع';
            case self::TYPE_SYSTEM:
                return $this->content;
            default:
                return 'رسالة';
        }
    }
}
