<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MessageReadReceipt extends BaseModel
{
    protected $table = 'message_read_receipts';

    protected $fillable = [
        'message_id',
        'customer_id',
        'read_at',
    ];

    protected $casts = [
        'read_at' => 'datetime',
    ];

    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
}
