<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoryView extends BaseModel
{
    protected $table = 'story_views';

    protected $fillable = [
        'story_id',
        'viewer_id',
        'ip_address',
        'viewed_at',
    ];

    protected $casts = [
        'viewed_at' => 'datetime',
    ];

    public function story(): BelongsTo
    {
        return $this->belongsTo(UserStory::class, 'story_id');
    }

    public function viewer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'viewer_id');
    }
}
