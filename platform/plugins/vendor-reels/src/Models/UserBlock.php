<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserBlock extends BaseModel
{
    protected $table = 'user_blocks';

    protected $fillable = [
        'blocker_id',
        'blocked_id',
        'reason',
        'blocked_at',
    ];

    protected $casts = [
        'blocked_at' => 'datetime',
    ];

    public function blocker(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'blocker_id');
    }

    public function blocked(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'blocked_id');
    }

    public function scopeByBlocker($query, $blockerId)
    {
        return $query->where('blocker_id', $blockerId);
    }

    public function scopeByBlocked($query, $blockedId)
    {
        return $query->where('blocked_id', $blockedId);
    }
}
