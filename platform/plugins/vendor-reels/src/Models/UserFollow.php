<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserFollow extends BaseModel
{
    protected $table = 'user_follows';

    protected $fillable = [
        'follower_id',
        'following_id',
        'status',
        'followed_at',
        'unfollowed_at',
    ];

    protected $casts = [
        'followed_at' => 'datetime',
        'unfollowed_at' => 'datetime',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_ACCEPTED = 'accepted';
    const STATUS_BLOCKED = 'blocked';
    const STATUS_UNFOLLOWED = 'unfollowed';

    public function follower(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'follower_id');
    }

    public function following(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'following_id');
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', self::STATUS_ACCEPTED);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeBlocked($query)
    {
        return $query->where('status', self::STATUS_BLOCKED);
    }

    public function accept(): void
    {
        $this->update([
            'status' => self::STATUS_ACCEPTED,
            'followed_at' => now()
        ]);
    }

    public function block(): void
    {
        $this->update([
            'status' => self::STATUS_BLOCKED
        ]);
    }

    public function unfollow(): void
    {
        $this->update([
            'status' => self::STATUS_UNFOLLOWED,
            'unfollowed_at' => now()
        ]);
    }
}
