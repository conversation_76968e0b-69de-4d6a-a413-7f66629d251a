<?php

namespace Botble\VendorReels\Models;

use Bo<PERSON>ble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class UserNotification extends BaseModel
{
    protected $table = 'user_notifications';

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'notifiable_type',
        'notifiable_id',
        'action_url',
        'is_read',
        'read_at',
        'priority',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    // أنواع الإشعارات
    const TYPE_MESSAGE = 'message';
    const TYPE_FOLLOW = 'follow';
    const TYPE_REEL_LIKE = 'reel_like';
    const TYPE_REEL_COMMENT = 'reel_comment';
    const TYPE_REEL_SHARE = 'reel_share';
    const TYPE_STORY_VIEW = 'story_view';
    const TYPE_MENTION = 'mention';
    const TYPE_SYSTEM = 'system';
    const TYPE_PROMOTION = 'promotion';

    // مستويات الأولوية
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    public function user(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'user_id');
    }

    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now()
        ]);
    }

    public function getIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_MESSAGE => '💬',
            self::TYPE_FOLLOW => '👥',
            self::TYPE_REEL_LIKE => '❤️',
            self::TYPE_REEL_COMMENT => '💭',
            self::TYPE_REEL_SHARE => '🔄',
            self::TYPE_STORY_VIEW => '👁️',
            self::TYPE_MENTION => '📢',
            self::TYPE_SYSTEM => '⚙️',
            self::TYPE_PROMOTION => '🎯',
            default => '🔔',
        };
    }

    public function getColorAttribute(): string
    {
        return match($this->priority) {
            self::PRIORITY_LOW => '#6B7280',
            self::PRIORITY_NORMAL => '#3B82F6',
            self::PRIORITY_HIGH => '#F59E0B',
            self::PRIORITY_URGENT => '#EF4444',
            default => '#3B82F6',
        };
    }

    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }
}
