<?php

namespace Bo<PERSON>ble\VendorReels\Models;

use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Botble\Marketplace\Models\Store;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Botble\Media\Facades\RvMedia;

class UserStory extends BaseModel
{
    protected $table = 'user_stories';

    protected $fillable = [
        'user_id',
        'store_id',
        'content',
        'media_url',
        'media_type',
        'background_color',
        'text_color',
        'duration',
        'views_count',
        'expires_at',
        'is_active',
        'privacy',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'views_count' => 'integer',
        'duration' => 'integer',
    ];

    const MEDIA_TYPE_IMAGE = 'image';
    const MEDIA_TYPE_VIDEO = 'video';
    const MEDIA_TYPE_TEXT = 'text';

    const PRIVACY_PUBLIC = 'public';
    const PRIVACY_FOLLOWERS = 'followers';
    const PRIVACY_CLOSE_FRIENDS = 'close_friends';
    const PRIVACY_PRIVATE = 'private';

    public function user(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'user_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function views(): HasMany
    {
        return $this->hasMany(StoryView::class, 'story_id');
    }

    public function getMediaUrlAttribute($value): ?string
    {
        if ($value && in_array($this->media_type, [self::MEDIA_TYPE_IMAGE, self::MEDIA_TYPE_VIDEO])) {
            return RvMedia::getImageUrl($value);
        }

        return $value;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('expires_at', '>', now());
    }

    public function scopePublic($query)
    {
        return $query->where('privacy', self::PRIVACY_PUBLIC);
    }

    public function scopeForFollowers($query)
    {
        return $query->where('privacy', self::PRIVACY_FOLLOWERS);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByStore($query, $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    public function incrementViews(): void
    {
        $this->increment('views_count');
    }

    public function addView($viewerId, $ipAddress = null): void
    {
        // تجنب تسجيل مشاهدات متكررة من نفس المستخدم
        $existingView = $this->views()
            ->where('viewer_id', $viewerId)
            ->first();

        if (!$existingView) {
            StoryView::create([
                'story_id' => $this->id,
                'viewer_id' => $viewerId,
                'ip_address' => $ipAddress,
                'viewed_at' => now()
            ]);

            $this->incrementViews();
        }
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function canView($userId): bool
    {
        // صاحب القصة يمكنه مشاهدتها دائماً
        if ($this->user_id === $userId) {
            return true;
        }

        // التحقق من انتهاء الصلاحية
        if ($this->isExpired() || !$this->is_active) {
            return false;
        }

        switch ($this->privacy) {
            case self::PRIVACY_PUBLIC:
                return true;

            case self::PRIVACY_FOLLOWERS:
                return UserFollow::where('follower_id', $userId)
                    ->where('following_id', $this->user_id)
                    ->accepted()
                    ->exists();

            case self::PRIVACY_CLOSE_FRIENDS:
                // يمكن إضافة منطق الأصدقاء المقربين لاحقاً
                return false;

            case self::PRIVACY_PRIVATE:
                return false;

            default:
                return false;
        }
    }

    public function getTimeRemaining(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return $this->expires_at->diffInSeconds(now());
    }
}
