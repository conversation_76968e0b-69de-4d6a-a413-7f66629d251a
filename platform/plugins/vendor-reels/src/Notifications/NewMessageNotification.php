<?php

namespace Bo<PERSON>ble\VendorReels\Notifications;

use Bo<PERSON><PERSON>\VendorReels\Models\Message;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewMessageNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public Message $message)
    {
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $senderName = $this->message->sender->name;
        $conversationTitle = $this->message->conversation->title ?? 'محادثة';
        $previewText = $this->message->getPreviewText();

        return (new MailMessage)
            ->subject("رسالة جديدة من {$senderName}")
            ->greeting("مرحباً {$notifiable->name}!")
            ->line("تلقيت رسالة جديدة من {$senderName} في {$conversationTitle}")
            ->line("محتوى الرسالة: {$previewText}")
            ->action('عرض المحادثة', url('/conversations/' . $this->message->conversation_id))
            ->line('شكراً لاستخدامك تطبيقنا!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'new_message',
            'message_id' => $this->message->id,
            'conversation_id' => $this->message->conversation_id,
            'sender_id' => $this->message->sender_id,
            'sender_name' => $this->message->sender->name,
            'sender_avatar' => $this->message->sender->avatar_url,
            'message_type' => $this->message->type,
            'message_preview' => $this->message->getPreviewText(),
            'conversation_title' => $this->message->conversation->title,
            'created_at' => $this->message->created_at->toISOString(),
        ];
    }
}
